<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agentic ORM - Redirecting...</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .pulse-animation {
            animation: pulse 2s ease-in-out infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center">
    <div class="text-center text-white">
        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-6 pulse-animation">
            <i data-lucide="brain-circuit" class="w-8 h-8 text-white"></i>
        </div>
        <h1 class="text-3xl font-bold mb-2">Agentic ORM</h1>
        <p class="text-blue-200 mb-6">Intelligent Social Media Management</p>
        <div class="flex items-center justify-center space-x-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>Redirecting to login...</span>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Check if user is already logged in
        const sessionToken = localStorage.getItem('session_token');
        
        if (sessionToken) {
            // Verify session and redirect to dashboard
            fetch('http://localhost:8001/api/v1/auth/me', {
                headers: {
                    'Authorization': `Bearer ${sessionToken}`
                }
            })
            .then(response => {
                if (response.ok) {
                    // Valid session, go to dashboard
                    window.location.href = 'dashboard.html';
                } else {
                    // Invalid session, go to login
                    localStorage.removeItem('session_token');
                    localStorage.removeItem('current_user');
                    window.location.href = 'login.html';
                }
            })
            .catch(error => {
                // Error checking session, go to login
                console.error('Session check error:', error);
                window.location.href = 'login.html';
            });
        } else {
            // No session, go to login
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1500);
        }
    </script>
</body>
</html>
