<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Insights & Reports - Agentic ORM</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .card { @apply bg-white rounded-lg shadow-md border border-gray-200 p-6; }
        .btn-primary { @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors; }
        .btn-secondary { @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors; }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            border: 1px solid;
            z-index: 1000;
            max-width: 400px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .notification.success {
            background-color: #d1fae5;
            border-color: #a7f3d0;
            color: #065f46;
        }
        .notification.error {
            background-color: #fee2e2;
            border-color: #fecaca;
            color: #991b1b;
        }
        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .app-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            background: white;
            border-bottom: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            height: 5rem;
        }
        .app-body {
            margin-top: 5rem;
            min-height: calc(100vh - 5rem);
        }
        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            height: 3rem;
        }
        .top-nav-menu {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            height: 100%;
            max-width: 80rem;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .top-nav-item {
            position: relative;
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: #374151;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            border-radius: 0.5rem;
            margin: 0 0.25rem;
        }
        .top-nav-item:hover {
            background-color: #f3f4f6;
            color: #1d4ed8;
        }
        .top-nav-item.active {
            background-color: #dbeafe;
            color: #1d4ed8;
            font-weight: 600;
        }
        .top-nav-item-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }
        .main-content {
            flex: 1;
            background: #f9fafb;
            width: 100%;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="flex items-center justify-between h-20 px-6">
                <div class="flex items-center space-x-4">
                    <button id="hamburger" class="hamburger lg:hidden p-2 rounded-md hover:bg-gray-100">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="bar-chart-3" class="w-8 h-8 text-blue-600"></i>
                        <h1 class="text-xl font-bold text-gray-900">User Insights & Business Intelligence</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="refresh-btn" class="btn-secondary flex items-center space-x-2">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        <span>Refresh</span>
                    </button>
                    <button class="btn-secondary flex items-center space-x-2">
                        <i data-lucide="filter" class="w-4 h-4"></i>
                        <span>Filter</span>
                    </button>
                    <button class="btn-secondary flex items-center space-x-2">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        <span>Export</span>
                    </button>
                </div>
            </div>

            <!-- Top Navigation -->
            <nav class="top-nav">
                <div class="top-nav-menu">
                    <a href="dashboard.html" class="top-nav-item">
                        <i data-lucide="home" class="top-nav-item-icon"></i>
                        Dashboard
                    </a>
                    <a href="responses.html" class="top-nav-item">
                        <i data-lucide="message-square" class="top-nav-item-icon"></i>
                        Responses
                    </a>
                    <a href="learning.html" class="top-nav-item">
                        <i data-lucide="brain" class="top-nav-item-icon"></i>
                        AI Learning
                    </a>
                    <a href="reporting.html" class="top-nav-item active">
                        <i data-lucide="bar-chart-3" class="top-nav-item-icon"></i>
                        Reports
                    </a>
                    <a href="settings.html" class="top-nav-item">
                        <i data-lucide="settings" class="top-nav-item-icon"></i>
                        Settings
                    </a>
                </div>
            </nav>
        </header>

        <!-- Main Content -->
        <div class="app-body">
            <main class="main-content p-6">
                <!-- Loading State -->
                <div id="loading" class="flex items-center justify-center h-64">
                    <i data-lucide="loader-2" class="w-8 h-8 animate-spin mr-2"></i>
                    <span>Loading user insights...</span>
                </div>

                <!-- Main Content -->
                <div id="content" class="hidden space-y-6">
                    <!-- Summary Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                                    <p id="total-users" class="text-2xl font-bold text-gray-900">0</p>
                                    <p class="text-xs text-gray-500">Active in last 30 days</p>
                                </div>
                                <i data-lucide="users" class="w-8 h-8 text-blue-600"></i>
                            </div>
                        </div>

                        <div class="card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">High Engagement</p>
                                    <p id="high-engagement" class="text-2xl font-bold text-gray-900">0</p>
                                    <p class="text-xs text-gray-500">Users with 5+ interactions</p>
                                </div>
                                <i data-lucide="trending-up" class="w-8 h-8 text-green-600"></i>
                            </div>
                        </div>

                        <div class="card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Negative Sentiment</p>
                                    <p id="negative-sentiment" class="text-2xl font-bold text-red-600">0</p>
                                    <p class="text-xs text-gray-500">Require attention</p>
                                </div>
                                <i data-lucide="trending-down" class="w-8 h-8 text-red-600"></i>
                            </div>
                        </div>

                        <div class="card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Multilingual</p>
                                    <p id="multilingual-users" class="text-2xl font-bold text-gray-900">0</p>
                                    <p class="text-xs text-gray-500">Non-English users</p>
                                </div>
                                <i data-lucide="globe" class="w-8 h-8 text-purple-600"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Sentiment Distribution -->
                        <div class="card">
                            <h3 class="text-lg font-semibold mb-4">Sentiment Distribution</h3>
                            <div class="h-64">
                                <canvas id="sentiment-chart"></canvas>
                            </div>
                        </div>

                        <!-- Language Performance -->
                        <div class="card">
                            <h3 class="text-lg font-semibold mb-4">Language Performance</h3>
                            <div class="h-64">
                                <canvas id="language-chart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Trending Issues -->
                    <div class="card">
                        <h3 class="text-lg font-semibold mb-4 flex items-center space-x-2">
                            <i data-lucide="alert-triangle" class="w-5 h-5"></i>
                            <span>Trending Issues (Last 7 Days)</span>
                        </h3>
                        <div id="trending-issues" class="space-y-3">
                            <!-- Issues will be populated here -->
                        </div>
                    </div>

                    <!-- User Details Table -->
                    <div class="card">
                        <h3 class="text-lg font-semibold mb-4">User Engagement Details</h3>
                        <div id="user-details" class="space-y-3">
                            <!-- User details will be populated here -->
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        let userInsights = null;
        let sentimentChart = null;
        let languageChart = null;

        // DOM elements
        const loading = document.getElementById('loading');
        const content = document.getElementById('content');

        // Fetch user insights
        async function fetchUserInsights() {
            try {
                const response = await fetch('/api/v1/reporting/user-insights');
                const data = await response.json();
                userInsights = data;
                updateUI();
            } catch (error) {
                console.error('Error fetching user insights:', error);
                showNotification('Error loading user insights', 'error');
            } finally {
                loading.classList.add('hidden');
                content.classList.remove('hidden');
            }
        }

        // Update UI with data
        function updateUI() {
            if (!userInsights) return;

            // Update summary cards
            document.getElementById('total-users').textContent = userInsights.summary?.total_users || 0;
            document.getElementById('high-engagement').textContent = userInsights.summary?.high_engagement_users || 0;
            document.getElementById('negative-sentiment').textContent = userInsights.summary?.negative_sentiment_users || 0;
            document.getElementById('multilingual-users').textContent = userInsights.summary?.multilingual_users || 0;

            // Update charts
            updateSentimentChart();
            updateLanguageChart();
            
            // Update trending issues
            updateTrendingIssues();
            
            // Update user details
            updateUserDetails();
        }

        // Update sentiment chart
        function updateSentimentChart() {
            const ctx = document.getElementById('sentiment-chart').getContext('2d');
            
            if (sentimentChart) {
                sentimentChart.destroy();
            }

            // Calculate sentiment distribution
            const sentimentCounts = userInsights.user_insights?.reduce((acc, user) => {
                acc[user.sentiment_category] = (acc[user.sentiment_category] || 0) + 1;
                return acc;
            }, {}) || {};

            const data = {
                labels: ['Positive', 'Negative', 'Neutral'],
                datasets: [{
                    data: [
                        sentimentCounts.positive || 0,
                        sentimentCounts.negative || 0,
                        sentimentCounts.neutral || 0
                    ],
                    backgroundColor: ['#10B981', '#EF4444', '#F59E0B'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            };

            sentimentChart = new Chart(ctx, {
                type: 'doughnut',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Update language chart
        function updateLanguageChart() {
            const ctx = document.getElementById('language-chart').getContext('2d');
            
            if (languageChart) {
                languageChart.destroy();
            }

            const languageData = userInsights.language_insights?.map(lang => ({
                language: lang.language === 'en' ? 'English' : 
                         lang.language === 'ta' ? 'Tamil' :
                         lang.language === 'hi' ? 'Hindi' :
                         lang.language === 'es' ? 'Spanish' :
                         lang.language === 'fr' ? 'French' : lang.language,
                posts: lang.post_count
            })) || [];

            const data = {
                labels: languageData.map(d => d.language),
                datasets: [{
                    label: 'Posts',
                    data: languageData.map(d => d.posts),
                    backgroundColor: '#3B82F6',
                    borderColor: '#1D4ED8',
                    borderWidth: 1
                }]
            };

            languageChart = new Chart(ctx, {
                type: 'bar',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Update trending issues
        function updateTrendingIssues() {
            const container = document.getElementById('trending-issues');
            
            if (!userInsights.trending_issues || userInsights.trending_issues.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-4">No trending issues found.</p>';
                return;
            }

            container.innerHTML = '';
            
            userInsights.trending_issues.forEach(issue => {
                const urgencyColor = issue.urgency_score >= 70 ? 'bg-red-100 text-red-800' :
                                   issue.urgency_score >= 40 ? 'bg-yellow-100 text-yellow-800' :
                                   'bg-green-100 text-green-800';
                
                const urgencyText = issue.urgency_score >= 70 ? 'High' :
                                  issue.urgency_score >= 40 ? 'Medium' : 'Low';

                const div = document.createElement('div');
                div.className = 'flex items-center justify-between p-3 border rounded-lg';
                div.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded">${issue.scenario}</span>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">${issue.language.toUpperCase()}</span>
                        <span class="text-sm">${issue.frequency} mentions</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="text-right">
                            <div class="text-sm font-medium">${issue.negative_count} negative</div>
                            <div class="text-xs text-gray-500">${issue.urgency_score}% urgency</div>
                        </div>
                        <span class="px-2 py-1 text-xs font-medium rounded ${urgencyColor}">${urgencyText}</span>
                    </div>
                `;
                container.appendChild(div);
            });
        }

        // Update user details
        function updateUserDetails() {
            const container = document.getElementById('user-details');
            
            if (!userInsights.user_insights || userInsights.user_insights.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-4">No user data found.</p>';
                return;
            }

            container.innerHTML = '';
            
            userInsights.user_insights.slice(0, 10).forEach(user => {
                const sentimentColor = user.sentiment_category === 'positive' ? 'bg-green-100 text-green-800' :
                                     user.sentiment_category === 'negative' ? 'bg-red-100 text-red-800' :
                                     'bg-gray-100 text-gray-800';
                
                const engagementColor = user.engagement_level === 'high' ? 'bg-blue-100 text-blue-800' :
                                      user.engagement_level === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-gray-100 text-gray-800';

                const div = document.createElement('div');
                div.className = 'flex items-center justify-between p-3 border rounded-lg';
                div.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <div>
                            <div class="font-medium">@${user.username}</div>
                            <div class="text-sm text-gray-500">${user.display_name}</div>
                        </div>
                        <span class="px-2 py-1 text-xs font-medium rounded ${sentimentColor}">${user.sentiment_category}</span>
                        <span class="px-2 py-1 text-xs font-medium rounded ${engagementColor}">${user.engagement_level}</span>
                        ${user.language !== 'en' ? `<span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded">${user.language.toUpperCase()}</span>` : ''}
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium">${user.total_posts} posts</div>
                        <div class="text-xs text-gray-500">${user.response_rate}% response rate</div>
                        <div class="text-xs text-gray-500">Score: ${user.avg_sentiment_score}</div>
                    </div>
                `;
                container.appendChild(div);
            });
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Event listeners
        document.getElementById('refresh-btn').addEventListener('click', () => {
            loading.classList.remove('hidden');
            content.classList.add('hidden');
            fetchUserInsights();
        });

        // Initialize
        fetchUserInsights();
        
        // Auto-refresh every minute
        setInterval(fetchUserInsights, 60000);
    </script>
</body>
</html>
