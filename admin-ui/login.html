<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agentic ORM - Intelligent Social Media Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite alternate;
        }
        @keyframes pulse-glow {
            from { box-shadow: 0 0 20px rgba(102, 126, 234, 0.4); }
            to { box-shadow: 0 0 30px rgba(102, 126, 234, 0.8); }
        }
        .slide-in {
            animation: slideIn 0.8s ease-out;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <!-- Background Pattern -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full floating-animation"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-5 rounded-full floating-animation" style="animation-delay: -3s;"></div>
        <div class="absolute top-1/2 left-1/4 w-32 h-32 bg-white opacity-10 rounded-full floating-animation" style="animation-delay: -1s;"></div>
    </div>

    <!-- Main Container -->
    <div class="relative z-10 w-full max-w-6xl mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            
            <!-- Left Side - Branding & Features -->
            <div class="text-white space-y-8 slide-in">
                <!-- Logo & Brand -->
                <div class="text-center lg:text-left">
                    <div class="flex items-center justify-center lg:justify-start mb-6">
                        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mr-4 pulse-glow">
                            <i data-lucide="brain-circuit" class="w-8 h-8 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-4xl font-bold">Agentic ORM</h1>
                            <p class="text-blue-200 text-lg">Intelligent Social Media Management</p>
                        </div>
                    </div>
                    
                    <p class="text-xl text-blue-100 leading-relaxed mb-8">
                        Powered by Microsoft Phi-3 Mini for privacy-focused, intelligent social media automation and customer service management.
                    </p>
                </div>

                <!-- Key Features -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="glass-effect rounded-xl p-6">
                        <div class="w-12 h-12 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center mb-4">
                            <i data-lucide="shield-check" class="w-6 h-6 text-green-300"></i>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">100% Privacy</h3>
                        <p class="text-blue-100 text-sm">Complete local processing with Microsoft Phi-3 Mini. Your data never leaves your infrastructure.</p>
                    </div>
                    
                    <div class="glass-effect rounded-xl p-6">
                        <div class="w-12 h-12 bg-purple-500 bg-opacity-20 rounded-lg flex items-center justify-center mb-4">
                            <i data-lucide="zap" class="w-6 h-6 text-purple-300"></i>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">AI-Powered</h3>
                        <p class="text-blue-100 text-sm">Advanced sentiment analysis and intelligent response generation for superior customer engagement.</p>
                    </div>
                    
                    <div class="glass-effect rounded-xl p-6">
                        <div class="w-12 h-12 bg-orange-500 bg-opacity-20 rounded-lg flex items-center justify-center mb-4">
                            <i data-lucide="users" class="w-6 h-6 text-orange-300"></i>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">Role-Based Access</h3>
                        <p class="text-blue-100 text-sm">Admin, Infrastructure, and Marketing roles with granular permissions and account assignments.</p>
                    </div>
                    
                    <div class="glass-effect rounded-xl p-6">
                        <div class="w-12 h-12 bg-blue-500 bg-opacity-20 rounded-lg flex items-center justify-center mb-4">
                            <i data-lucide="activity" class="w-6 h-6 text-blue-300"></i>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">Real-Time Monitoring</h3>
                        <p class="text-blue-100 text-sm">Live system health monitoring, performance analytics, and comprehensive dashboard insights.</p>
                    </div>
                </div>

                <!-- Stats -->
                <div class="glass-effect rounded-xl p-6">
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-white">10K+</div>
                            <div class="text-blue-200 text-sm">Daily Responses</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-white">99.9%</div>
                            <div class="text-blue-200 text-sm">Uptime</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-white">24/7</div>
                            <div class="text-blue-200 text-sm">Monitoring</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="slide-in" style="animation-delay: 0.3s;">
                <div class="glass-effect rounded-2xl p-8 max-w-md mx-auto">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-bold text-white mb-2">Welcome Back</h2>
                        <p class="text-blue-200">Sign in to access your Agentic ORM dashboard</p>
                    </div>

                    <!-- Login Form -->
                    <form id="login-form" class="space-y-6">
                        <div>
                            <label class="block text-white text-sm font-medium mb-2">Username</label>
                            <div class="relative">
                                <input type="text" name="username" required 
                                       class="w-full px-4 py-3 pl-12 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent"
                                       placeholder="Enter your username">
                                <i data-lucide="user" class="w-5 h-5 text-blue-200 absolute left-4 top-3.5"></i>
                            </div>
                        </div>

                        <div>
                            <label class="block text-white text-sm font-medium mb-2">Password</label>
                            <div class="relative">
                                <input type="password" name="password" required 
                                       class="w-full px-4 py-3 pl-12 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent"
                                       placeholder="Enter your password">
                                <i data-lucide="lock" class="w-5 h-5 text-blue-200 absolute left-4 top-3.5"></i>
                            </div>
                        </div>

                        <button type="submit" 
                                class="w-full bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50">
                            <span class="flex items-center justify-center">
                                <i data-lucide="log-in" class="w-5 h-5 mr-2"></i>
                                Sign In
                            </span>
                        </button>
                    </form>

                    <!-- Demo Credentials -->
                    <div class="mt-8 p-4 bg-blue-900 bg-opacity-30 rounded-lg border border-blue-400 border-opacity-30">
                        <h4 class="text-white font-semibold mb-3 flex items-center">
                            <i data-lucide="info" class="w-4 h-4 mr-2"></i>
                            Demo Credentials
                        </h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between items-center">
                                <span class="text-blue-200">Admin:</span>
                                <span class="text-white font-mono">admin / admin123</span>
                            </div>
                            <div class="text-xs text-blue-300 mt-2">
                                Full system access including user management
                            </div>
                        </div>
                    </div>

                    <!-- Loading Indicator -->
                    <div id="loading-indicator" class="hidden mt-4 text-center">
                        <div class="inline-flex items-center text-white">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Signing in...
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div id="error-message" class="hidden mt-4 p-3 bg-red-500 bg-opacity-20 border border-red-400 border-opacity-30 rounded-lg">
                        <div class="flex items-center text-red-200">
                            <i data-lucide="alert-circle" class="w-4 h-4 mr-2"></i>
                            <span id="error-text">Login failed. Please try again.</span>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="text-center mt-8 text-blue-200 text-sm">
                    <p>&copy; 2024 Agentic ORM. Built with privacy and security in mind.</p>
                    <div class="flex items-center justify-center mt-2 space-x-4">
                        <span class="flex items-center">
                            <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                            All systems operational
                        </span>
                        <span class="flex items-center">
                            <i data-lucide="shield" class="w-3 h-3 mr-1"></i>
                            100% Local Processing
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';

        // Initialize Lucide icons
        lucide.createIcons();

        // Login form handler
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const credentials = {
                username: formData.get('username'),
                password: formData.get('password')
            };

            // Show loading
            document.getElementById('loading-indicator').classList.remove('hidden');
            document.getElementById('error-message').classList.add('hidden');

            try {
                const response = await fetch(`${API_BASE}/api/v1/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(credentials)
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    // Store session token
                    localStorage.setItem('session_token', data.session_token);
                    localStorage.setItem('current_user', JSON.stringify(data.user));
                    
                    // Show success and redirect
                    showSuccess('Login successful! Redirecting...');
                    
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);
                } else {
                    showError(data.error || 'Login failed. Please check your credentials.');
                }
            } catch (error) {
                console.error('Login error:', error);
                showError('Connection failed. Please check if the system is running.');
            } finally {
                document.getElementById('loading-indicator').classList.add('hidden');
            }
        });

        function showError(message) {
            document.getElementById('error-text').textContent = message;
            document.getElementById('error-message').classList.remove('hidden');
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'mt-4 p-3 bg-green-500 bg-opacity-20 border border-green-400 border-opacity-30 rounded-lg';
            successDiv.innerHTML = `
                <div class="flex items-center text-green-200">
                    <i data-lucide="check-circle" class="w-4 h-4 mr-2"></i>
                    <span>${message}</span>
                </div>
            `;
            
            const form = document.getElementById('login-form');
            form.parentNode.insertBefore(successDiv, form.nextSibling);
            
            // Re-initialize icons
            lucide.createIcons();
        }

        // Check if already logged in
        window.addEventListener('load', function() {
            const sessionToken = localStorage.getItem('session_token');
            if (sessionToken) {
                // Verify session is still valid
                fetch(`${API_BASE}/api/v1/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${sessionToken}`
                    }
                })
                .then(response => {
                    if (response.ok) {
                        // Already logged in, redirect to dashboard
                        window.location.href = 'dashboard.html';
                    }
                })
                .catch(error => {
                    // Session invalid, clear storage
                    localStorage.removeItem('session_token');
                    localStorage.removeItem('current_user');
                });
            }
        });

        // Auto-fill demo credentials on demo button click
        function fillDemoCredentials() {
            document.querySelector('input[name="username"]').value = 'admin';
            document.querySelector('input[name="password"]').value = 'admin123';
        }

        // Add click handler to demo credentials
        document.querySelector('.bg-blue-900').addEventListener('click', fillDemoCredentials);
    </script>
</body>
</html>
