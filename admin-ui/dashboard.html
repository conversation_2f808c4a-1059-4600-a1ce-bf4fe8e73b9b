<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agentic ORM - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .card { @apply bg-white rounded-lg shadow-md border border-gray-200 p-6; }
        .btn-primary { @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors; }
        .btn-secondary { @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors; }

        /* Layout Styles */
        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .app-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            background: white;
            border-bottom: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            height: 5rem; /* Increased header height for top nav */
        }

        .app-body {
            margin-top: 5rem; /* Header height */
            min-height: calc(100vh - 5rem);
        }

        /* Top Navigation Styles */
        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            height: 3rem;
        }

        .top-nav-menu {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            height: 100%;
            max-width: 80rem;
            margin: 0 auto;
            padding: 0 1rem;
        }

        @media (min-width: 640px) {
            .top-nav-menu {
                padding: 0 1.5rem;
            }
        }

        @media (min-width: 1024px) {
            .top-nav-menu {
                padding: 0 2rem;
            }
        }

        .top-nav-item {
            position: relative;
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: #374151;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            border-radius: 0.5rem;
            margin: 0 0.25rem;
        }

        .top-nav-item:hover {
            background-color: #f3f4f6;
            color: #1d4ed8;
        }

        .top-nav-item.active {
            background-color: #dbeafe;
            color: #1d4ed8;
            font-weight: 600;
        }

        .top-nav-item-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }

        /* Dropdown Styles */
        .dropdown {
            position: relative;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 60;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease-in-out;
            min-width: 200px;
        }

        .dropdown:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            border-radius: 0.375rem;
            margin: 0.25rem;
        }

        .dropdown-item:hover {
            background-color: #f3f4f6;
            color: #1d4ed8;
        }

        .dropdown-item-icon {
            width: 1rem;
            height: 1rem;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }

        /* Sidebar Styles (Mobile Only) */
        .sidebar {
            width: 16rem;
            background: white;
            border-right: 1px solid #e5e7eb;
            transition: transform 0.3s ease-in-out;
            flex-shrink: 0;
            display: none; /* Hidden on desktop */
            flex-direction: column;
            overflow-y: auto;
        }

        .sidebar-closed {
            transform: translateX(-100%);
        }

        .main-content {
            flex: 1;
            background: #f9fafb;
            width: 100%;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            margin: 0.25rem 0.5rem;
            color: #374151;
            border-radius: 0.5rem;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            text-decoration: none;
        }

        .nav-item:hover {
            background-color: #eff6ff;
            color: #1d4ed8;
        }

        .nav-item.active {
            background-color: #dbeafe;
            color: #1d4ed8;
            border-right: 2px solid #1d4ed8;
        }

        .nav-item-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }

        /* Ensure sidebar content is properly contained */
        .sidebar nav {
            min-height: 0; /* Allow flexbox to shrink */
        }

        /* Mobile Styles */
        .hamburger {
            display: none;
        }

        .sidebar-overlay {
            display: none;
        }

        @media (max-width: 1024px) {
            .hamburger {
                display: block;
            }

            .top-nav {
                display: none; /* Hide top nav on mobile */
            }

            .sidebar {
                display: flex; /* Show sidebar on mobile */
                position: fixed !important;
                top: 0;
                left: 0;
                bottom: 0;
                height: 100vh !important;
                z-index: 40;
                box-shadow: 2px 0 10px rgba(0,0,0,0.1);
                padding-top: 5rem; /* Account for header */
            }

            .sidebar-closed {
                transform: translateX(-100%);
            }

            .sidebar-overlay {
                position: fixed;
                inset: 0;
                background: rgba(0,0,0,0.5);
                z-index: 35;
            }

            .sidebar-overlay.hidden {
                display: none;
            }

            .main-content {
                margin-left: 0;
                width: 100%;
            }
        }

        /* Filter and Search Styles */
        .quick-filter-tag {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
        }

        .quick-filter-tag.active {
            border-color: currentColor;
            box-shadow: 0 0 0 1px currentColor;
        }

        .filter-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            background-color: #3b82f6;
            color: white;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .filter-badge button {
            margin-left: 0.25rem;
            color: white;
            opacity: 0.8;
        }

        .filter-badge button:hover {
            opacity: 1;
        }



        .response-card.filtered-out {
            display: none;
        }

        .response-card.priority-high {
            border-left: 4px solid #ef4444;
        }

        .response-card.priority-medium {
            border-left: 4px solid #f59e0b;
        }

        .response-card.priority-low {
            border-left: 4px solid #10b981;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            border: 1px solid;
            z-index: 1000;
            max-width: 400px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .notification.success {
            background-color: #d1fae5;
            border-color: #a7f3d0;
            color: #065f46;
        }

        .notification.info {
            background-color: #dbeafe;
            border-color: #93c5fd;
            color: #1e40af;
        }

        .notification.warning {
            background-color: #fef3c7;
            border-color: #fde68a;
            color: #92400e;
        }

        .notification.error {
            background-color: #fee2e2;
            border-color: #fecaca;
            color: #991b1b;
        }

        /* Real-time search and filter animations */
        #advanced-filters {
            transition: all 0.3s ease-in-out;
            overflow: hidden;
        }

        #toggle-filters-icon {
            transition: transform 0.3s ease-in-out;
        }

        #search-input {
            transition: all 0.2s ease-in-out;
        }

        #search-input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }



        /* Loading animation for search */
        @keyframes pulse-search {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .search-loading {
            animation: pulse-search 1.5s ease-in-out infinite;
        }

        /* Response card animations */
        .response-card {
            transition: all 0.2s ease-in-out;
        }

        .response-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Filter badge animations */
        .filter-badge {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-10px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Quick filter tag hover effects */
        .quick-filter-tag {
            transition: all 0.2s ease-in-out;
        }

        .quick-filter-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Results count animation */
        #results-count {
            transition: all 0.3s ease-in-out;
        }

        .results-updated {
            color: #059669;
            font-weight: 600;
        }

        /* Prevent HTML flashing during rendering */
        #responses-list {
            min-height: 200px;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        #responses-list.loaded {
            opacity: 1;
        }

        /* Loading state for responses */
        .responses-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
            color: #6b7280;
        }

        .responses-loading .spinner {
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Modern Response Cards */
        .response-card-modern {
            transition: all 0.2s ease-in-out;
        }

        .response-card-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-clamp: 2;
        }

        /* Grid View Styles */
        .responses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1rem;
        }

        .responses-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .response-card-grid {
            height: auto;
            min-height: 280px;
        }

        .response-card-list {
            width: 100%;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease-in-out;
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: 1rem;
            max-width: 4xl;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            transform: scale(0.9);
            transition: transform 0.3s ease-in-out;
        }

        .modal-overlay.active .modal-content {
            transform: scale(1);
        }

        .search-highlight, mark.search-highlight {
            background-color: #fef3c7;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-weight: 500;
        }

        /* Sticky Search Bar Enhancements */
        .sticky-search-bar {
            transition: all 0.2s ease-in-out;
        }

        .sticky-search-bar.scrolled {
            background: rgba(255, 255, 255, 0.98) !important;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
            border-color: #d1d5db !important;
        }

        /* Smooth scroll behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Loading animation for infinite scroll */
        .loading-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: .5;
            }
        }

        /* Infinite scroll enhancements */
        .infinite-scroll-container {
            position: relative;
        }

        .scroll-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 30;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 50%;
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
        }

        .scroll-to-top.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .scroll-to-top:hover {
            background: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        /* Smooth scrolling and prevent glitches */
        html {
            scroll-behavior: smooth;
        }

        body {
            overflow-x: hidden; /* Prevent horizontal scroll glitches */
        }

        /* Ensure proper spacing at bottom */
        .main-content {
            min-height: calc(100vh - 200px);
            padding-bottom: 2rem;
        }

        /* Loading indicator improvements */
        #loading-indicator {
            transition: opacity 0.3s ease-in-out;
        }

        /* End of results styling */
        #end-of-results {
            transition: all 0.3s ease-in-out;
            margin-bottom: 2rem;
        }

        /* Footer spacing */
        footer {
            margin-top: auto;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Response Detail Modal -->
    <div id="response-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="p-6">
                <!-- Modal Header -->
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Response Details</h2>
                    <button onclick="closeResponseModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <!-- Modal Content -->
                <div id="modal-content-body">
                    <!-- Content will be populated dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- Image Preview Modal -->
    <div id="image-preview-modal" class="modal-overlay">
        <div class="modal-content max-w-4xl">
            <div class="p-6">
                <!-- Modal Header -->
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900">Image Preview</h2>
                    <button onclick="closeImagePreview()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <!-- Image Content -->
                <div class="text-center">
                    <img id="preview-image" src="" alt="Preview" class="max-w-full max-h-96 mx-auto rounded-lg shadow-lg">
                    <div class="mt-4 flex justify-center space-x-3">
                        <button onclick="analyzeImage()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                            <i data-lucide="brain" class="w-4 h-4 mr-2"></i>
                            Analyze with AI
                        </button>
                        <button onclick="downloadImage()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center">
                            <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                            Download
                        </button>
                    </div>
                </div>

                <!-- Analysis Results -->
                <div id="image-analysis-results" class="hidden mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-blue-900 mb-3">AI Analysis Results</h3>
                    <div id="analysis-content">
                        <!-- Analysis results will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <!-- Top Header Bar -->
            <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8 h-16 border-b border-gray-200">
                <div class="flex justify-between items-center h-full">
                    <div class="flex items-center" id="header-left-section">
                        <!-- Mobile hamburger menu -->
                        <button id="hamburger-menu" class="hamburger mr-4 text-gray-500 hover:text-gray-700">
                            <i data-lucide="menu" class="w-6 h-6"></i>
                        </button>

                        <div id="company-logo-header" class="mr-3 hidden">
                            <!-- Company logo will be inserted here -->
                        </div>
                        <h1 class="text-2xl lg:text-3xl font-bold text-gray-900" id="main-header-title">
                            🤖 Agentic ORM
                        </h1>
                    </div>
                    <div class="flex items-center space-x-4" id="header-right-section">
                        <!-- Desktop: Show system status and user info -->
                        <div class="hidden lg:flex items-center space-x-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <span class="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                                System Online
                            </span>
                            <!-- User info and logout button will be added here by JavaScript -->
                        </div>

                        <!-- Mobile: Show user avatar -->
                        <div class="lg:hidden" id="mobile-user-avatar">
                            <!-- User avatar will be added here by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Navigation (Desktop Only) -->
            <div class="hidden lg:block top-nav">
                <nav class="top-nav-menu">
                    <div class="top-nav-item active" onclick="showSection('dashboard')" data-section="dashboard">
                        <i data-lucide="home" class="top-nav-item-icon"></i>
                        <span>Dashboard</span>
                    </div>

                    <div class="top-nav-item" onclick="showSection('responses')" data-section="responses">
                        <i data-lucide="message-circle" class="top-nav-item-icon"></i>
                        <span>Response Management</span>
                    </div>

                    <div class="top-nav-item" onclick="showSection('social-accounts')" data-section="social-accounts">
                        <i data-lucide="users" class="top-nav-item-icon"></i>
                        <span>Social Accounts</span>
                    </div>

                    <div class="top-nav-item" onclick="showSection('analytics')" data-section="analytics">
                        <i data-lucide="bar-chart-3" class="top-nav-item-icon"></i>
                        <span>Analytics</span>
                    </div>

                    <!-- Administration Dropdown -->
                    <div class="dropdown">
                        <div class="top-nav-item">
                            <i data-lucide="settings" class="top-nav-item-icon"></i>
                            <span>Administration</span>
                            <i data-lucide="chevron-down" class="w-4 h-4 ml-1"></i>
                        </div>
                        <div class="dropdown-menu">
                            <div class="dropdown-item" onclick="showSection('system-status')" data-section="system-status">
                                <i data-lucide="activity" class="dropdown-item-icon"></i>
                                <span>System Status</span>
                            </div>
                            <div class="dropdown-item" onclick="showSection('model-management')" data-section="model-management">
                                <i data-lucide="brain" class="dropdown-item-icon"></i>
                                <span>Model Management</span>
                            </div>
                            <div class="dropdown-item" onclick="showSection('user-management')" data-section="user-management" id="user-management-nav-dropdown">
                                <i data-lucide="user-cog" class="dropdown-item-icon"></i>
                                <span>User Management</span>
                            </div>
                            <div class="dropdown-item" onclick="showSection('credential-management')" data-section="credential-management" id="credential-management-nav-dropdown">
                                <i data-lucide="shield-check" class="dropdown-item-icon"></i>
                                <span>Secure Credentials</span>
                            </div>
                            <div class="dropdown-item" onclick="showSection('my-profile')" data-section="my-profile">
                                <i data-lucide="user" class="dropdown-item-icon"></i>
                                <span>My Profile</span>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </header>

        <!-- App Body -->
        <div class="app-body">
            <!-- Mobile Sidebar Overlay -->
            <div id="sidebar-overlay" class="sidebar-overlay hidden"></div>

            <!-- Left Sidebar Navigation (Mobile Only) -->
            <aside id="sidebar" class="sidebar">
                <!-- Navigation Menu -->
                <nav class="flex-1 px-2 py-6 space-y-1 overflow-y-auto">
                    <div class="nav-item active" onclick="showSection('dashboard')" data-section="dashboard">
                        <i data-lucide="home" class="nav-item-icon"></i>
                        <span>Dashboard</span>
                    </div>

                    <div class="nav-item" onclick="showSection('responses')" data-section="responses">
                        <i data-lucide="message-circle" class="nav-item-icon"></i>
                        <span>Response Management</span>
                    </div>

                    <div class="nav-item" onclick="showSection('social-accounts')" data-section="social-accounts">
                        <i data-lucide="users" class="nav-item-icon"></i>
                        <span>Social Accounts</span>
                    </div>

                    <div class="nav-item" onclick="showSection('analytics')" data-section="analytics">
                        <i data-lucide="bar-chart-3" class="nav-item-icon"></i>
                        <span>Analytics</span>
                    </div>

                    <!-- User Profile Section -->
                    <div class="border-t border-gray-200 pt-4 mt-4">
                        <div class="nav-item" onclick="showSection('my-profile')" data-section="my-profile">
                            <i data-lucide="user" class="nav-item-icon"></i>
                            <span>My Profile</span>
                        </div>
                    </div>

                    <!-- Admin Only Sections -->
                    <div class="border-t border-gray-200 pt-4 mt-4">
                        <p class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">Administration</p>

                        <div class="nav-item" onclick="showSection('system-status')" data-section="system-status">
                            <i data-lucide="activity" class="nav-item-icon"></i>
                            <span>System Status</span>
                        </div>

                        <div class="nav-item" onclick="showSection('model-management')" data-section="model-management">
                            <i data-lucide="brain" class="nav-item-icon"></i>
                            <span>Model Management</span>
                        </div>

                        <div class="nav-item" onclick="showSection('user-management')" data-section="user-management" id="user-management-nav" style="display: none;">
                            <i data-lucide="user-cog" class="nav-item-icon"></i>
                            <span>User Management</span>
                        </div>

                        <div class="nav-item" onclick="showSection('credential-management')" data-section="credential-management" id="credential-management-nav" style="display: none;">
                            <i data-lucide="shield-check" class="nav-item-icon"></i>
                            <span>Secure Credentials</span>
                        </div>
                    </div>
                </nav>

                <!-- Sidebar Footer (Mobile Only) -->
                <div class="border-t border-gray-200 p-4 flex-shrink-0">
                    <!-- User Info (Mobile Only) -->
                    <div id="sidebar-user-section" class="mb-4 hidden">
                        <div class="flex items-center space-x-3 mb-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <i data-lucide="user" class="w-4 h-4 text-blue-600"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate" id="sidebar-username">
                                    <!-- Username will be inserted here -->
                                </p>
                                <p class="text-xs text-gray-500" id="sidebar-user-role">
                                    <!-- User role will be inserted here -->
                                </p>
                            </div>
                        </div>

                        <!-- Logout Button (Mobile) -->
                        <button onclick="logout()" class="w-full flex items-center justify-center px-3 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors">
                            <i data-lucide="log-out" class="w-4 h-4 mr-2"></i>
                            Logout
                        </button>

                        <div class="border-t border-gray-200 my-3"></div>
                    </div>

                    <!-- System Status (Mobile Only) -->
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-xs text-gray-500">System Status</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <span class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></span>
                            Online
                        </span>
                    </div>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="main-content">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Dashboard Section -->
        <div id="dashboard" class="section">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Dashboard Overview</h2>
                
                <!-- Enhanced Stats Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="card hover:shadow-lg transition-shadow">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="p-3 bg-blue-100 rounded-lg">
                                    <i data-lucide="message-square" class="h-6 w-6 text-blue-600"></i>
                                </div>
                            </div>
                            <div class="ml-4 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Pending Responses</dt>
                                    <dd class="text-2xl font-bold text-gray-900" data-stat="pending-responses">0</dd>
                                    <dd class="text-xs text-blue-600 font-medium">+2 from yesterday</dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="card hover:shadow-lg transition-shadow">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="p-3 bg-green-100 rounded-lg">
                                    <i data-lucide="check-circle" class="h-6 w-6 text-green-600"></i>
                                </div>
                            </div>
                            <div class="ml-4 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Approved Today</dt>
                                    <dd class="text-2xl font-bold text-gray-900" data-stat="approved-today">0</dd>
                                    <dd class="text-xs text-green-600 font-medium">+15% vs yesterday</dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="card hover:shadow-lg transition-shadow">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="p-3 bg-purple-100 rounded-lg">
                                    <i data-lucide="trending-up" class="h-6 w-6 text-purple-600"></i>
                                </div>
                            </div>
                            <div class="ml-4 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Posts Detected</dt>
                                    <dd class="text-2xl font-bold text-gray-900" data-stat="posts-detected">0</dd>
                                    <dd class="text-xs text-purple-600 font-medium">Last 24 hours</dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="card hover:shadow-lg transition-shadow">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="p-3 bg-orange-100 rounded-lg">
                                    <i data-lucide="zap" class="h-6 w-6 text-orange-600"></i>
                                </div>
                            </div>
                            <div class="ml-4 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Avg Response Time</dt>
                                    <dd class="text-2xl font-bold text-gray-900" data-stat="avg-response-time">1.2s</dd>
                                    <dd class="text-xs text-orange-600 font-medium">AI Processing</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Stats Row -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">System Status</p>
                                <p class="text-lg font-semibold text-green-600">🟢 All Systems Operational</p>
                            </div>
                            <div class="p-2 bg-green-100 rounded-lg">
                                <i data-lucide="shield-check" class="h-5 w-5 text-green-600"></i>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Model Health</p>
                                <p class="text-lg font-semibold text-blue-600">🧠 Phi-3 Mini Active</p>
                            </div>
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <i data-lucide="cpu" class="h-5 w-5 text-blue-600"></i>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Data Privacy</p>
                                <p class="text-lg font-semibold text-purple-600">🔒 100% Local</p>
                            </div>
                            <div class="p-2 bg-purple-100 rounded-lg">
                                <i data-lucide="lock" class="h-5 w-5 text-purple-600"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts and Analytics -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Response Trends Chart -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Response Trends (7 Days)</h3>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span class="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                                    Live Data
                                </span>
                            </div>
                        </div>
                        <div class="h-64">
                            <canvas id="responseTrendsChart"></canvas>
                        </div>
                    </div>

                    <!-- Sentiment Analysis Chart -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Sentiment Distribution</h3>
                            <div class="text-sm text-gray-500">Last 30 days</div>
                        </div>
                        <div class="h-64">
                            <canvas id="sentimentChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                    <!-- AI Performance -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">AI Performance</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Accuracy Score</span>
                                <span class="text-sm font-medium text-green-600">94.2%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 94.2%"></div>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Response Quality</span>
                                <span class="text-sm font-medium text-blue-600">91.8%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 91.8%"></div>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Context Understanding</span>
                                <span class="text-sm font-medium text-purple-600">96.5%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-600 h-2 rounded-full" style="width: 96.5%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
                        <div class="space-y-3" id="recent-activity">
                            <!-- Activity items will be populated here -->
                        </div>
                    </div>

                    <!-- System Resources -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">System Resources</h3>
                        <div class="space-y-4">
                            <div>
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm text-gray-600">Memory Usage</span>
                                    <span class="text-sm font-medium">2.1 GB / 8 GB</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-orange-500 h-2 rounded-full" style="width: 26.25%"></div>
                                </div>
                            </div>

                            <div>
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm text-gray-600">CPU Usage</span>
                                    <span class="text-sm font-medium">15%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 15%"></div>
                                </div>
                            </div>

                            <div>
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm text-gray-600">API Rate Limit</span>
                                    <span class="text-sm font-medium">45 / 300</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 15%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <button onclick="showSection('responses')" class="btn-primary flex items-center justify-center">
                            <i data-lucide="clock" class="w-4 h-4 mr-2"></i>
                            View Pending Responses
                        </button>
                        <button onclick="showSection('social-accounts')" class="btn-secondary flex items-center justify-center">
                            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                            Add Social Account
                        </button>
                        <button onclick="showSection('analytics')" class="btn-secondary flex items-center justify-center">
                            <i data-lucide="trending-up" class="w-4 h-4 mr-2"></i>
                            View Analytics
                        </button>
                        <button onclick="triggerManualMonitoring()" class="btn-secondary flex items-center justify-center">
                            <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                            Manual Check
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Social Accounts Section -->
        <div id="social-accounts" class="section hidden">
            <div class="mb-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Social Media Accounts</h2>
                    <div class="flex space-x-3">
                        <!-- Monitoring Controls -->
                        <div class="flex items-center space-x-2 bg-gray-100 px-4 py-2 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">Monitoring:</span>
                            <button id="monitoring-toggle" onclick="toggleMonitoring()" class="flex items-center space-x-2 px-3 py-1 rounded-md text-sm font-medium transition-colors">
                                <i id="monitoring-icon" data-lucide="play" class="w-4 h-4"></i>
                                <span id="monitoring-text">Enabled</span>
                            </button>
                        </div>
                        <button onclick="triggerManualMonitoring()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center">
                            <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                            Manual Check
                        </button>
                        <button onclick="loadRealSocialAccounts()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center">
                            <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                            Refresh
                        </button>
                        <button onclick="showAddAccountModal()" class="btn-primary flex items-center">
                            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                            Add Account
                        </button>
                    </div>
                </div>

                <!-- Monitoring Status Bar -->
                <div id="monitoring-status" class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg hidden">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i data-lucide="info" class="w-5 h-5 text-blue-600 mr-2"></i>
                            <span id="monitoring-status-text" class="text-blue-800 font-medium">Monitoring Status</span>
                        </div>
                        <div class="text-sm text-blue-600">
                            <span id="monitoring-last-trigger">Last manual trigger: Never</span>
                        </div>
                    </div>
                </div>

                <!-- Accounts Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="accounts-grid">
                    <!-- Account cards will be populated here -->
                </div>
            </div>
        </div>

        <!-- Response Management Section -->
        <div id="responses" class="section hidden">
            <div class="mb-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Response Management</h2>
                    <div class="flex space-x-3">
                        <!-- View Toggle -->
                        <div class="flex items-center bg-gray-100 rounded-lg p-1">
                            <button id="list-view-btn" onclick="toggleView('list')" class="px-3 py-1 rounded text-sm font-medium transition-colors bg-white text-gray-900 shadow-sm">
                                <i data-lucide="list" class="w-4 h-4 mr-1"></i>
                                List
                            </button>
                            <button id="grid-view-btn" onclick="toggleView('grid')" class="px-3 py-1 rounded text-sm font-medium transition-colors text-gray-600 hover:text-gray-900">
                                <i data-lucide="grid-3x3" class="w-4 h-4 mr-1"></i>
                                Grid
                            </button>
                        </div>
                        <button onclick="regenerateAllResponses()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center">
                            <i data-lucide="refresh-ccw" class="w-4 h-4 mr-2"></i>
                            Regenerate All
                        </button>
                        <button onclick="exportResponses()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center">
                            <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                            Export
                        </button>
                        <button onclick="loadRealResponses()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                            <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                            Refresh
                        </button>
                    </div>
                </div>

                <!-- Advanced Filters & Search (Sticky) -->
                <div id="sticky-search-bar" class="sticky-search-bar sticky top-0 z-20 bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg shadow-lg mb-6 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Search & Filters</h3>
                        <div class="flex items-center space-x-3">
                            <button onclick="clearAllFilters()" class="text-sm text-gray-500 hover:text-gray-700">
                                Clear All Filters
                            </button>
                            <button onclick="toggleAdvancedFilters()" id="toggle-filters-btn" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                                <span id="toggle-filters-text">Show Filters</span>
                                <i data-lucide="chevron-down" id="toggle-filters-icon" class="w-4 h-4 ml-1"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Search Bar -->
                    <div class="mb-4">
                        <div class="relative">
                            <input type="text" id="search-input" placeholder="Search by tweet ID, name, content, date, sentiment..."
                                   class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg"
                                   oninput="handleRealTimeSearch(event)"
                                   onkeyup="handleSearchInput(event)">
                            <i data-lucide="search" class="w-5 h-5 text-gray-400 absolute left-3 top-3.5"></i>
                            <div id="search-loading" class="hidden absolute right-3 top-3.5">
                                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                            </div>
                        </div>
                        <!-- Search suggestions/hints -->
                        <div class="mt-2 text-sm text-gray-500">
                            <span class="font-medium">Search tips:</span>
                            Try "flight delay", "@username", "negative", "today", or tweet ID
                        </div>
                    </div>

                    <!-- Quick Filter Tags (Always Visible) -->
                    <div class="mb-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                        <div class="flex items-center flex-wrap gap-2">
                            <span class="text-sm font-medium text-gray-700 mr-2">Quick Filters:</span>
                            <button onclick="applyQuickFilter('pending')" class="quick-filter-tag bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
                                ⏳ Pending Review
                            </button>
                            <button onclick="applyQuickFilter('urgent')" class="quick-filter-tag bg-red-100 text-red-800 hover:bg-red-200">
                                🚨 Urgent
                            </button>
                            <button onclick="applyQuickFilter('complaints')" class="quick-filter-tag bg-orange-100 text-orange-800 hover:bg-orange-200">
                                😠 Complaints
                            </button>
                            <button onclick="applyQuickFilter('delays')" class="quick-filter-tag bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
                                ⏰ Flight Delays
                            </button>
                            <button onclick="applyQuickFilter('baggage')" class="quick-filter-tag bg-blue-100 text-blue-800 hover:bg-blue-200">
                                🧳 Baggage Issues
                            </button>
                            <button onclick="applyQuickFilter('refunds')" class="quick-filter-tag bg-green-100 text-green-800 hover:bg-green-200">
                                💰 Refund Requests
                            </button>
                            <button onclick="applyQuickFilter('praise')" class="quick-filter-tag bg-purple-100 text-purple-800 hover:bg-purple-200">
                                👏 Praise
                            </button>
                        </div>
                    </div>

                    <!-- Advanced Filters (Collapsible) -->
                    <div id="advanced-filters" class="hidden">
                        <!-- Filter Row 1 -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                            <!-- Status Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                <select id="status-filter" onchange="applyFilters()" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500">
                                    <option value="">All Statuses</option>
                                    <option value="pending">Pending Review</option>
                                    <option value="approved">Approved</option>
                                    <option value="posted">Posted</option>
                                    <option value="rejected">Rejected</option>
                                </select>
                            </div>

                            <!-- Sentiment Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Sentiment</label>
                                <select id="sentiment-filter" onchange="applyFilters()" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500">
                                    <option value="">All Sentiments</option>
                                    <option value="negative">Negative</option>
                                    <option value="neutral">Neutral</option>
                                    <option value="positive">Positive</option>
                                </select>
                            </div>

                            <!-- Platform Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Platform</label>
                                <select id="platform-filter" onchange="applyFilters()" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500">
                                    <option value="">All Platforms</option>
                                    <option value="twitter">Twitter</option>
                                    <option value="facebook">Facebook</option>
                                    <option value="instagram">Instagram</option>
                                    <option value="linkedin">LinkedIn</option>
                                </select>
                            </div>

                            <!-- Priority Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                                <select id="priority-filter" onchange="applyFilters()" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500">
                                    <option value="">All Priorities</option>
                                    <option value="high">High Priority</option>
                                    <option value="medium">Medium Priority</option>
                                    <option value="low">Low Priority</option>
                                </select>
                            </div>
                        </div>

                        <!-- Filter Row 2 -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <!-- Date Range Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                                <select id="date-filter" onchange="applyFilters()" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500">
                                    <option value="">All Time</option>
                                    <option value="today">Today</option>
                                    <option value="yesterday">Yesterday</option>
                                    <option value="last7days">Last 7 Days</option>
                                    <option value="last30days">Last 30 Days</option>
                                    <option value="custom">Custom Range</option>
                                </select>
                            </div>

                            <!-- Response Time Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Response Time</label>
                                <select id="response-time-filter" onchange="applyFilters()" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500">
                                    <option value="">All Response Times</option>
                                    <option value="under1h">Under 1 Hour</option>
                                    <option value="1to4h">1-4 Hours</option>
                                    <option value="4to24h">4-24 Hours</option>
                                    <option value="over24h">Over 24 Hours</option>
                                </select>
                            </div>

                            <!-- Confidence Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">AI Confidence</label>
                                <select id="confidence-filter" onchange="applyFilters()" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500">
                                    <option value="">All Confidence Levels</option>
                                    <option value="high">High (90%+)</option>
                                    <option value="medium">Medium (70-89%)</option>
                                    <option value="low">Low (50-69%)</option>
                                    <option value="very-low">Very Low (<50%)</option>
                                </select>
                            </div>
                        </div>


                    </div>

                    <!-- Active Filters Display -->
                    <div id="active-filters" class="hidden">
                        <div class="flex items-center gap-2 mb-2">
                            <span class="text-sm font-medium text-gray-700">Active Filters:</span>
                            <div id="active-filters-list" class="flex flex-wrap gap-1"></div>
                        </div>
                    </div>

                    <!-- Results Summary -->
                    <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                        <div class="text-sm text-gray-600">
                            <span id="results-count">0</span> responses found
                            <span id="filtered-count" class="hidden"> (filtered from <span id="total-count">0</span> total)</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <label class="text-sm text-gray-700">Sort by:</label>
                            <select id="sort-filter" onchange="applyFilters()" class="border border-gray-300 rounded px-2 py-1 text-sm">
                                <option value="newest">Newest First</option>
                                <option value="oldest">Oldest First</option>
                                <option value="priority">Priority</option>
                                <option value="sentiment">Sentiment</option>
                                <option value="confidence">AI Confidence</option>
                                <option value="response-time">Response Time</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Responses List -->
                <div class="space-y-4" id="responses-list">
                    <!-- Response cards will be populated here -->
                </div>

                <!-- Loading Indicator -->
                <div id="loading-indicator" class="hidden flex justify-center items-center py-8 transition-all duration-300">
                    <div class="flex items-center space-x-3 text-gray-500 bg-white rounded-lg shadow-sm border border-gray-200 px-6 py-3">
                        <div class="animate-spin rounded-full h-5 w-5 border-2 border-blue-600 border-t-transparent"></div>
                        <span class="text-sm font-medium">Loading more responses...</span>
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
                            <div class="w-2 h-2 bg-blue-600 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
                            <div class="w-2 h-2 bg-blue-600 rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
                        </div>
                    </div>
                </div>

                <!-- End of Results Indicator -->
                <div id="end-of-results" class="hidden text-center py-12 text-gray-500 bg-gray-50 rounded-lg border border-gray-200 mx-4 mb-8">
                    <div class="flex flex-col items-center space-y-3">
                        <div class="p-3 bg-green-100 rounded-full">
                            <i data-lucide="check-circle" class="h-6 w-6 text-green-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">You've reached the end!</p>
                            <p class="text-xs text-gray-500 mt-1">All responses have been loaded.</p>
                        </div>
                        <button onclick="scrollToTop()" class="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                            <i data-lucide="arrow-up" class="w-4 h-4 mr-1"></i>
                            Back to top
                        </button>
                    </div>
                </div>

                <!-- Scroll to Top Button (Fixed Position) -->
                <button id="scroll-to-top" class="scroll-to-top" onclick="scrollToTop()">
                    <i data-lucide="arrow-up" class="w-5 h-5"></i>
                </button>

                <!-- Scroll Padding for Better UX -->
                <div class="h-20"></div>
            </div>
        </div>

        <!-- System Status Section -->
        <div id="system-status" class="section hidden">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">System Status & Health</h1>
                    <p class="mt-2 text-gray-600">Monitor and manage all system components in real-time</p>
                </div>

                <!-- Status Overview Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <!-- Overall System Status -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Overall Status</h3>
                            <div id="overall-status-indicator" class="w-4 h-4 rounded-full bg-gray-400"></div>
                        </div>
                        <div id="overall-status-text" class="text-2xl font-bold text-gray-500">Checking...</div>
                        <div id="overall-status-details" class="text-sm text-gray-600 mt-2">Initializing system check...</div>
                    </div>

                    <!-- Last Check Time -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Last Health Check</h3>
                            <i data-lucide="clock" class="w-5 h-5 text-gray-400"></i>
                        </div>
                        <div id="last-check-time" class="text-2xl font-bold text-gray-900">Never</div>
                        <div class="text-sm text-gray-600 mt-2">
                            <button onclick="refreshSystemStatus()" class="text-blue-600 hover:text-blue-800 font-medium">
                                <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-1"></i>
                                Refresh Now
                            </button>
                        </div>
                    </div>

                    <!-- Auto Refresh -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Auto Refresh</h3>
                            <div class="flex items-center">
                                <input type="checkbox" id="auto-refresh-toggle" class="mr-2" onchange="toggleAutoRefresh()">
                                <label for="auto-refresh-toggle" class="text-sm text-gray-600">Enable</label>
                            </div>
                        </div>
                        <div id="auto-refresh-status" class="text-2xl font-bold text-gray-500">Disabled</div>
                        <div class="text-sm text-gray-600 mt-2">Refresh every 30 seconds</div>
                    </div>
                </div>

                <!-- Debug Mode Toggle -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Debug Mode</h3>
                            <div class="flex items-center">
                                <input type="checkbox" id="debug-mode-toggle" class="mr-2" onchange="toggleDebugMode()">
                                <label for="debug-mode-toggle" class="text-sm text-gray-600">Enable</label>
                            </div>
                        </div>
                        <div id="debug-mode-status" class="text-2xl font-bold text-gray-500">Disabled</div>
                        <div class="text-sm text-gray-600 mt-2">Show detailed logs and API calls</div>
                    </div>

                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Live Monitoring</h3>
                            <div class="flex items-center space-x-2">
                                <button onclick="triggerManualMonitoring()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    <i data-lucide="play" class="w-4 h-4 inline mr-1"></i>
                                    Manual Check
                                </button>
                            </div>
                        </div>
                        <div id="monitoring-status-text" class="text-lg font-bold text-gray-900">Ready</div>
                        <div class="text-sm text-gray-600 mt-2">
                            <span id="last-monitoring-check">Never checked</span>
                        </div>
                    </div>
                </div>

                <!-- Service Status Cards -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Backend API Status -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div id="backend-status-indicator" class="w-3 h-3 rounded-full bg-gray-400 mr-3"></div>
                                <h3 class="text-lg font-medium text-gray-900">Backend API</h3>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span id="backend-status-badge" class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-600">Unknown</span>
                                <button onclick="checkBackendStatus()" class="text-blue-600 hover:text-blue-800">
                                    <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Endpoint:</span>
                                <span class="font-mono text-gray-900">http://localhost:8001</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Response Time:</span>
                                <span id="backend-response-time" class="text-gray-900">-</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Last Check:</span>
                                <span id="backend-last-check" class="text-gray-900">Never</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">AI Model:</span>
                                <span id="backend-model" class="text-gray-900">-</span>
                            </div>
                        </div>

                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex space-x-2">
                                <button id="backend-start-btn" onclick="startBackendService()" class="btn-secondary text-sm hidden">
                                    <i data-lucide="play" class="w-4 h-4 mr-1"></i>
                                    Start Service
                                </button>
                                <button id="backend-stop-btn" onclick="stopBackendService()" class="btn-secondary text-sm hidden">
                                    <i data-lucide="stop" class="w-4 h-4 mr-1"></i>
                                    Stop Service
                                </button>
                                <button onclick="openBackendLogs()" class="btn-secondary text-sm">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-1"></i>
                                    View Logs
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Admin UI Status -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div id="adminui-status-indicator" class="w-3 h-3 rounded-full bg-green-400 mr-3"></div>
                                <h3 class="text-lg font-medium text-gray-900">Admin UI</h3>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span id="adminui-status-badge" class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-600">Running</span>
                                <button onclick="checkAdminUIStatus()" class="text-blue-600 hover:text-blue-800">
                                    <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Endpoint:</span>
                                <span class="font-mono text-gray-900">http://localhost:3000</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Response Time:</span>
                                <span id="adminui-response-time" class="text-gray-900">-</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Last Check:</span>
                                <span id="adminui-last-check" class="text-gray-900">Never</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Status:</span>
                                <span id="adminui-details" class="text-gray-900">Active (You're using it!)</span>
                            </div>
                        </div>

                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex space-x-2">
                                <button onclick="openAdminUILogs()" class="btn-secondary text-sm">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-1"></i>
                                    View Logs
                                </button>
                                <button onclick="window.location.reload()" class="btn-secondary text-sm">
                                    <i data-lucide="refresh-cw" class="w-4 h-4 mr-1"></i>
                                    Reload UI
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Resources -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                    <!-- Database Status -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div id="database-status-indicator" class="w-3 h-3 rounded-full bg-gray-400 mr-3"></div>
                                <h3 class="text-lg font-medium text-gray-900">Database</h3>
                            </div>
                            <button onclick="checkDatabaseStatus()" class="text-blue-600 hover:text-blue-800">
                                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                            </button>
                        </div>

                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Responses:</span>
                                <span id="db-responses-count" class="text-gray-900">-</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Posts:</span>
                                <span id="db-posts-count" class="text-gray-900">-</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Accounts:</span>
                                <span id="db-accounts-count" class="text-gray-900">-</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Pending:</span>
                                <span id="db-pending-count" class="text-gray-900">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- AI Model Status -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div id="ai-status-indicator" class="w-3 h-3 rounded-full bg-gray-400 mr-3"></div>
                                <h3 class="text-lg font-medium text-gray-900">AI Model</h3>
                            </div>
                            <button onclick="checkAIModelStatus()" class="text-blue-600 hover:text-blue-800">
                                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                            </button>
                        </div>

                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Model:</span>
                                <span id="ai-model-name" class="text-gray-900">Phi-3 Mini</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Privacy:</span>
                                <span id="ai-privacy-status" class="text-gray-900">100% Local</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Status:</span>
                                <span id="ai-model-status" class="text-gray-900">-</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Last Response:</span>
                                <span id="ai-last-response" class="text-gray-900">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- System Performance -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div id="performance-status-indicator" class="w-3 h-3 rounded-full bg-gray-400 mr-3"></div>
                                <h3 class="text-lg font-medium text-gray-900">Performance</h3>
                            </div>
                            <button onclick="checkPerformanceStatus()" class="text-blue-600 hover:text-blue-800">
                                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                            </button>
                        </div>

                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">API Latency:</span>
                                <span id="perf-api-latency" class="text-gray-900">-</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Memory Usage:</span>
                                <span id="perf-memory" class="text-gray-900">-</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Uptime:</span>
                                <span id="perf-uptime" class="text-gray-900">-</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Load:</span>
                                <span id="perf-load" class="text-gray-900">Normal</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Actions -->
                <div class="card mb-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">System Actions</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <button onclick="startFullSystem()" class="btn-primary flex items-center justify-center">
                            <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                            Start All Services
                        </button>
                        <button onclick="stopFullSystem()" class="btn-secondary flex items-center justify-center">
                            <i data-lucide="stop" class="w-4 h-4 mr-2"></i>
                            Stop All Services
                        </button>
                        <button onclick="restartFullSystem()" class="btn-secondary flex items-center justify-center">
                            <i data-lucide="rotate-cw" class="w-4 h-4 mr-2"></i>
                            Restart System
                        </button>
                        <button onclick="downloadSystemLogs()" class="btn-secondary flex items-center justify-center">
                            <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                            Download Logs
                        </button>
                    </div>
                </div>

                <!-- System Logs Viewer -->
                <div class="card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Live System Logs</h3>
                        <div class="flex items-center space-x-2">
                            <select id="log-source" class="text-sm border border-gray-300 rounded px-2 py-1">
                                <option value="backend">Backend Logs</option>
                                <option value="adminui">Admin UI Logs</option>
                                <option value="system">System Logs</option>
                            </select>
                            <button onclick="refreshLogs()" class="text-blue-600 hover:text-blue-800">
                                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                            </button>
                            <button onclick="clearLogs()" class="text-red-600 hover:text-red-800">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>

                    <div id="logs-container" class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm h-64 overflow-y-auto">
                        <div class="text-gray-500">Loading logs...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Section -->
        <div id="analytics" class="section hidden">
            <div class="mb-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Analytics & Insights</h2>
                    <button onclick="loadRealAnalytics()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                        <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                        Refresh Data
                    </button>
                </div>

                <!-- Summary Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="card">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <i data-lucide="message-square" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Posts</p>
                                <p class="text-2xl font-bold text-gray-900" data-stat="weekly-posts">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <i data-lucide="smile" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Positive</p>
                                <p class="text-2xl font-bold text-gray-900" data-stat="sentiment-positive-count">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="flex items-center">
                            <div class="p-2 bg-red-100 rounded-lg">
                                <i data-lucide="frown" class="w-6 h-6 text-red-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Negative</p>
                                <p class="text-2xl font-bold text-gray-900" data-stat="sentiment-negative-count">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="flex items-center">
                            <div class="p-2 bg-gray-100 rounded-lg">
                                <i data-lucide="meh" class="w-6 h-6 text-gray-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Neutral</p>
                                <p class="text-2xl font-bold text-gray-900" data-stat="sentiment-neutral-count">0</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Sentiment Analysis -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Sentiment Distribution</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Positive</span>
                                <div class="flex items-center">
                                    <div class="w-32 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 0%" data-bar="sentiment-positive"></div>
                                    </div>
                                    <span class="text-sm font-medium" data-chart="sentiment-positive">0%</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Neutral</span>
                                <div class="flex items-center">
                                    <div class="w-32 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-gray-500 h-2 rounded-full" style="width: 0%" data-bar="sentiment-neutral"></div>
                                    </div>
                                    <span class="text-sm font-medium" data-chart="sentiment-neutral">0%</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Negative</span>
                                <div class="flex items-center">
                                    <div class="w-32 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-red-500 h-2 rounded-full" style="width: 0%" data-bar="sentiment-negative"></div>
                                    </div>
                                    <span class="text-sm font-medium" data-chart="sentiment-negative">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Platform Performance -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Platform Performance</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <i data-lucide="twitter" class="w-4 h-4 text-blue-500 mr-2"></i>
                                    <span class="text-sm text-gray-600">Twitter Posts</span>
                                </div>
                                <span class="text-sm font-medium" data-stat="twitter-posts">0</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <i data-lucide="send" class="w-4 h-4 text-green-500 mr-2"></i>
                                    <span class="text-sm text-gray-600">Twitter Responses</span>
                                </div>
                                <span class="text-sm font-medium" data-stat="twitter-responses">0</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <i data-lucide="calendar" class="w-4 h-4 text-purple-500 mr-2"></i>
                                    <span class="text-sm text-gray-600">Today's Activity</span>
                                </div>
                                <span class="text-sm font-medium" data-stat="today-posts">0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Weekly Summary -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Weekly Summary</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Total Posts</span>
                                <span class="text-lg font-bold text-gray-900" data-stat="weekly-posts">0</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Positive Mentions</span>
                                <span class="text-lg font-bold text-green-600" data-stat="weekly-positive">0</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Negative Mentions</span>
                                <span class="text-lg font-bold text-red-600" data-stat="weekly-negative">0</span>
                            </div>
                        </div>
                    </div>

                    <!-- AI Performance -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">AI Performance</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Text Model</span>
                                <span class="text-sm font-medium text-blue-600">Phi-3 Mini</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Vision Model</span>
                                <span class="text-sm font-medium text-purple-600">Phi-3.5 Vision</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Languages</span>
                                <span class="text-sm font-medium text-orange-600">🌍 22+ Supported</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Image Support</span>
                                <span class="text-sm font-medium text-green-600">✅ Enabled</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Privacy Status</span>
                                <span class="text-sm font-medium text-green-600">🔒 Local Only</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Avg Response Time</span>
                                <span class="text-sm font-medium">300-500ms</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Model Management Section -->
        <div id="model-management" class="section hidden">
            <div class="mb-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">AI Model Management</h2>
                    <div class="flex space-x-3">
                        <button onclick="checkModelUpdates()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                            <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                            Check for Updates
                        </button>
                        <button onclick="loadModelStatus()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center">
                            <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                            Refresh Status
                        </button>
                    </div>
                </div>

                <!-- Current Model Status -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Model Information -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Current Model</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Model Name</span>
                                <span class="text-sm font-medium" id="model-name">Loading...</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Model Type</span>
                                <span class="text-sm font-medium" id="model-type">Loading...</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">License</span>
                                <span class="text-sm font-medium text-green-600" id="model-license">Loading...</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Current Version</span>
                                <span class="text-sm font-medium" id="model-version">Loading...</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Last Updated</span>
                                <span class="text-sm font-medium" id="model-last-updated">Loading...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Security & Privacy -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Security & Privacy</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Data Processing</span>
                                <span class="text-sm font-medium text-green-600" id="privacy-status">🔒 100% Local</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">External AI Services</span>
                                <span class="text-sm font-medium text-green-600" id="data-security">✅ None Used</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Auto Updates</span>
                                <span class="text-sm font-medium text-orange-600">🔒 Manual Approval Required</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Security Verification</span>
                                <span class="text-sm font-medium text-green-600">✅ Enabled</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Backup System</span>
                                <span class="text-sm font-medium text-green-600">✅ Active</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Update Status -->
                <div id="update-status-card" class="card mb-6 hidden">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Update Available</h3>
                        <span id="update-badge" class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">New Version</span>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <i data-lucide="info" class="w-5 h-5 text-blue-600 mr-2"></i>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-blue-800" id="update-message">A new version of Phi-3 Mini is available</p>
                                    <p class="text-xs text-blue-600 mt-1" id="update-details">Security verification: Passed</p>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-600">
                                <span>Current: </span><span id="current-version-short">Loading...</span><br>
                                <span>Latest: </span><span id="latest-version-short">Loading...</span>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="dismissUpdate()" class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
                                    Dismiss
                                </button>
                                <button onclick="updateModel()" class="px-4 py-2 text-sm bg-green-600 text-white rounded hover:bg-green-700 flex items-center">
                                    <i data-lucide="download" class="w-4 h-4 mr-1"></i>
                                    Update Now
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Update History -->
                <div class="card">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Update History</h3>
                    <div id="update-history" class="space-y-3">
                        <div class="text-sm text-gray-500 text-center py-4">
                            Loading update history...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Management Section -->
        <div id="user-management" class="section hidden">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="mb-8">
                    <div class="flex justify-between items-center">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">User Management</h1>
                            <p class="mt-2 text-gray-600">Manage users and their access permissions</p>
                        </div>
                        <button onclick="showCreateUserModal()" class="btn-primary flex items-center">
                            <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                            Create User
                        </button>
                    </div>
                </div>

                <!-- User Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="card">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100">
                                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Users</p>
                                <p id="total-users-count" class="text-2xl font-semibold text-gray-900">-</p>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100">
                                <i data-lucide="shield-check" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Admin Users</p>
                                <p id="admin-users-count" class="text-2xl font-semibold text-gray-900">-</p>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100">
                                <i data-lucide="settings" class="w-6 h-6 text-purple-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Infra Users</p>
                                <p id="infra-users-count" class="text-2xl font-semibold text-gray-900">-</p>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-orange-100">
                                <i data-lucide="megaphone" class="w-6 h-6 text-orange-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Marketing Users</p>
                                <p id="marketing-users-count" class="text-2xl font-semibold text-gray-900">-</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="card">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-gray-900">All Users</h3>
                        <div class="flex items-center space-x-3">
                            <div class="relative">
                                <input type="text" id="user-search" placeholder="Search users..."
                                       class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <i data-lucide="search" class="w-4 h-4 text-gray-400 absolute left-3 top-3"></i>
                            </div>
                            <select id="role-filter" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500">
                                <option value="">All Roles</option>
                                <option value="admin">Admin</option>
                                <option value="infra">Infrastructure</option>
                                <option value="marketing">Marketing</option>
                            </select>
                            <button onclick="showCompanySettingsModal()" class="btn-secondary flex items-center">
                                <i data-lucide="building" class="w-4 h-4 mr-2"></i>
                                Company Settings
                            </button>
                            <button onclick="refreshUsers()" class="btn-secondary flex items-center">
                                <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                                Refresh
                            </button>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned Accounts</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="users-table-body" class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                        Loading users...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Credential Management Section -->
        <div id="credential-management" class="section hidden">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="mb-8">
                    <div class="flex justify-between items-center">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Secure Credential Management</h1>
                            <p class="mt-2 text-gray-600">Manage encrypted API credentials and sensitive tokens</p>
                        </div>
                        <button onclick="showAddCredentialModal()" class="btn-primary flex items-center">
                            <i data-lucide="shield-plus" class="w-4 h-4 mr-2"></i>
                            Add Credentials
                        </button>
                    </div>
                </div>

                <!-- Security Status -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="card">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100">
                                <i data-lucide="shield-check" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Encryption Status</p>
                                <p class="text-lg font-semibold text-green-600">AES-256 Encrypted</p>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100">
                                <i data-lucide="database" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Storage</p>
                                <p class="text-lg font-semibold text-blue-600">Database Encrypted</p>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100">
                                <i data-lucide="key" class="w-6 h-6 text-purple-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Master Key</p>
                                <p class="text-lg font-semibold text-purple-600">Secure</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Credentials Table -->
                <div class="card">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-gray-900">Stored Credentials</h3>
                        <div class="flex items-center space-x-3">
                            <select id="platform-filter" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500">
                                <option value="">All Platforms</option>
                                <option value="twitter">Twitter</option>
                                <option value="instagram">Instagram</option>
                                <option value="facebook">Facebook</option>
                                <option value="linkedin">LinkedIn</option>
                                <option value="system">System</option>
                            </select>
                            <button onclick="refreshCredentials()" class="btn-secondary flex items-center">
                                <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                                Refresh
                            </button>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credential</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Platform</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validation</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="credentials-table-body" class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                        Loading credentials...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Security Notice</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    <li>All credentials are encrypted using AES-256 encryption</li>
                                    <li>Master encryption key should be stored securely outside the application</li>
                                    <li>Regularly rotate credentials for enhanced security</li>
                                    <li>Monitor access logs for unauthorized credential access</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- My Profile Section -->
        <div id="my-profile" class="section hidden">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">My Profile</h1>
                    <p class="mt-2 text-gray-600">Manage your personal information and account settings</p>
                </div>

                <!-- Profile Overview Card -->
                <div class="card mb-8">
                    <div class="flex items-center space-x-6">
                        <div class="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center" id="profile-avatar">
                            <span class="text-white text-2xl font-medium" id="profile-avatar-text">U</span>
                        </div>
                        <div class="flex-1">
                            <h2 class="text-2xl font-bold text-gray-900" id="profile-display-name">Loading...</h2>
                            <p class="text-gray-600" id="profile-username">@loading</p>
                            <p class="text-gray-500" id="profile-email"><EMAIL></p>
                            <div class="mt-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" id="profile-role-badge">
                                    Loading...
                                </span>
                            </div>
                        </div>
                        <div>
                            <button onclick="showEditProfileModal()" class="btn-primary flex items-center">
                                <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
                                Edit Profile
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Profile Information -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Personal Information -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Full Name</label>
                                <p class="mt-1 text-sm text-gray-900" id="profile-full-name">Loading...</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Username</label>
                                <p class="mt-1 text-sm text-gray-900" id="profile-username-detail">@loading</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Email Address</label>
                                <p class="mt-1 text-sm text-gray-900" id="profile-email-detail"><EMAIL></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Role</label>
                                <p class="mt-1 text-sm text-gray-900" id="profile-role-detail">Loading...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Account Information -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Account Status</label>
                                <p class="mt-1 text-sm" id="profile-status">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                </p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Member Since</label>
                                <p class="mt-1 text-sm text-gray-900" id="profile-created-at">Loading...</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Last Login</label>
                                <p class="mt-1 text-sm text-gray-900" id="profile-last-login">Loading...</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Password</label>
                                <div class="mt-1 flex items-center space-x-3">
                                    <p class="text-sm text-gray-900">••••••••</p>
                                    <button onclick="showChangePasswordModal()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Change Password
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Assigned Social Accounts (for marketing users) -->
                <div id="profile-social-accounts-section" class="card mt-8 hidden">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Assigned Social Accounts</h3>
                    <div id="profile-social-accounts-list" class="space-y-3">
                        <!-- Social accounts will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Account Modal -->
    <div id="add-account-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 class="text-lg font-medium mb-4">Add Social Media Account</h3>
            <form id="add-account-form">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Platform</label>
                        <select name="platform" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="twitter">Twitter</option>
                            <option value="instagram">Instagram</option>
                            <option value="facebook">Facebook</option>
                            <option value="linkedin">LinkedIn</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                        <input type="text" name="username" placeholder="@username" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                        <input type="text" name="display_name" placeholder="Display Name" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="hideAddAccountModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Add Account</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Response Modal -->
    <div id="edit-response-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Edit Response</h3>
                    <button onclick="hideEditResponseModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <div class="mb-4">
                    <label for="edit-response-text" class="block text-sm font-medium text-gray-700 mb-2">
                        Response Text
                    </label>
                    <textarea
                        id="edit-response-text"
                        rows="4"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Edit your response here..."
                        oninput="updateCharacterCount()"
                    ></textarea>
                    <div class="flex justify-between items-center mt-2">
                        <span id="character-count" class="text-sm text-gray-500">0/280</span>
                        <span class="text-xs text-gray-400">Twitter character limit</span>
                    </div>
                </div>

                <div class="flex items-center justify-end space-x-3">
                    <button
                        onclick="hideEditResponseModal()"
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                    >
                        Cancel
                    </button>
                    <button
                        id="save-response-btn"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Settings Modal -->
    <div id="account-settings-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Account Settings</h3>
                    <button onclick="hideAccountSettingsModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <div class="mb-6">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <i data-lucide="twitter" class="w-6 h-6 text-blue-600" id="settings-platform-icon"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900" id="settings-account-name">@KarthikHar25823</h4>
                            <p class="text-sm text-gray-500" id="settings-platform-name">Twitter</p>
                        </div>
                    </div>
                </div>

                <div class="space-y-6">
                    <!-- Monitoring Settings -->
                    <div>
                        <h5 class="text-md font-medium text-gray-900 mb-3">Monitoring Settings</h5>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Monitor Mentions</label>
                                    <p class="text-xs text-gray-500">Automatically detect when this account is mentioned</p>
                                </div>
                                <input type="checkbox" id="monitor-mentions" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Auto-Generate Responses</label>
                                    <p class="text-xs text-gray-500">Automatically create AI responses for detected mentions</p>
                                </div>
                                <input type="checkbox" id="auto-generate" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Real-time Monitoring</label>
                                    <p class="text-xs text-gray-500">Monitor for new mentions every minute</p>
                                </div>
                                <input type="checkbox" id="realtime-monitoring" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Time Window (Hours)</label>
                                <select id="time-window-hours" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="1">1 hour</option>
                                    <option value="2" selected>2 hours</option>
                                    <option value="6">6 hours</option>
                                    <option value="12">12 hours</option>
                                    <option value="24">24 hours</option>
                                    <option value="0">All Time (No Time Limit)</option>
                                </select>
                                <p class="text-xs text-gray-500 mt-1">How far back to search for new tweets during monitoring</p>
                            </div>
                        </div>
                    </div>

                    <!-- Response Settings -->
                    <div>
                        <h5 class="text-md font-medium text-gray-900 mb-3">Response Settings</h5>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Response Tone</label>
                                <select id="response-tone" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                    <option value="professional">Professional</option>
                                    <option value="friendly" selected>Friendly</option>
                                    <option value="casual">Casual</option>
                                    <option value="formal">Formal</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Minimum Confidence Score</label>
                                <input type="range" id="confidence-threshold" min="0.5" max="1.0" step="0.05" value="0.8" class="w-full">
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>50%</span>
                                    <span id="confidence-value">80%</span>
                                    <span>100%</span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Only generate responses with confidence above this threshold</p>
                            </div>
                        </div>
                    </div>

                    <!-- Keywords & Hashtags -->
                    <div>
                        <h5 class="text-md font-medium text-gray-900 mb-3">Keywords & Hashtags</h5>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Monitor Keywords</label>
                                <input type="text" id="monitor-keywords" placeholder="AI, social media, automation (comma separated)" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <p class="text-xs text-gray-500 mt-1">Monitor posts containing these keywords</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Monitor Hashtags</label>
                                <input type="text" id="monitor-hashtags" placeholder="#AI, #socialmedia, #automation (comma separated)" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <p class="text-xs text-gray-500 mt-1">Monitor posts with these hashtags</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-end space-x-3 mt-8 pt-6 border-t">
                    <button onclick="hideAccountSettingsModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Cancel
                    </button>
                    <button onclick="saveAccountSettings()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Save Settings
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create User Modal -->
    <div id="create-user-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Create New User</h3>
                    <button onclick="hideCreateUserModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <form id="create-user-form" onsubmit="createUser(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                            <input type="text" name="full_name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="John Doe">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                            <input type="text" name="username" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="johndoe">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" name="email" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                            <select name="role" required onchange="toggleSocialAccountsSection(this.value)"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Select Role</option>
                                <option value="admin">Admin - Full access to all features</option>
                                <option value="infra">Infrastructure - System Status & Model Management</option>
                                <option value="marketing">Marketing - Response Management only</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <input type="password" name="password" required minlength="6"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Minimum 6 characters">
                    </div>

                    <!-- Social Accounts Assignment (only for marketing users) -->
                    <div id="social-accounts-section" class="hidden mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Assign Social Accounts</label>
                        <div class="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
                            <div id="social-accounts-list" class="space-y-2">
                                <!-- Social accounts will be loaded here -->
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Marketing users can only see responses from assigned accounts</p>
                    </div>

                    <div class="flex items-center justify-end space-x-3 mt-6">
                        <button type="button" onclick="hideCreateUserModal()" class="btn-secondary">
                            Cancel
                        </button>
                        <button type="submit" class="btn-primary">
                            Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div id="edit-user-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Edit User</h3>
                    <button onclick="hideEditUserModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <form id="edit-user-form" onsubmit="updateUser(event)">
                    <input type="hidden" id="edit-user-id" name="user_id">

                    <!-- Profile Image Section -->
                    <div class="mb-6 text-center">
                        <div class="relative inline-block">
                            <div id="edit-user-avatar" class="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3">
                                <span class="text-white text-2xl font-medium">A</span>
                            </div>
                            <label for="edit-profile-image" class="absolute bottom-0 right-0 bg-blue-600 text-white rounded-full p-2 cursor-pointer hover:bg-blue-700">
                                <i data-lucide="camera" class="w-4 h-4"></i>
                            </label>
                            <input type="file" id="edit-profile-image" name="profile_image" accept="image/*" class="hidden" onchange="previewEditImage(this)">
                        </div>
                        <p class="text-xs text-gray-500">Click camera icon to change profile picture</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                            <input type="text" id="edit-full-name" name="full_name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                            <input type="text" id="edit-username" name="username" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" id="edit-email" name="email" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                            <select id="edit-role" name="role" required onchange="toggleEditSocialAccountsSection(this.value)"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="admin">Admin - Full access to all features</option>
                                <option value="infra">Infrastructure - System Status & Model Management</option>
                                <option value="marketing">Marketing - Response Management only</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Job Title</label>
                        <input type="text" id="edit-job-title" name="job_title"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="e.g., Customer Service Manager">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Department</label>
                        <input type="text" id="edit-department" name="department"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="e.g., Customer Support">
                    </div>

                    <!-- Social Accounts Assignment (only for marketing users) -->
                    <div id="edit-social-accounts-section" class="hidden mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Assign Social Accounts</label>
                        <div class="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
                            <div id="edit-social-accounts-list" class="space-y-2">
                                <!-- Social accounts will be loaded here -->
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Marketing users can only see responses from assigned accounts</p>
                    </div>

                    <!-- Status Toggle -->
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="edit-is-active" name="is_active" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">User is active</span>
                        </label>
                    </div>

                    <div class="flex items-center justify-end space-x-3 mt-6">
                        <button type="button" onclick="hideEditUserModal()" class="btn-secondary">
                            Cancel
                        </button>
                        <button type="submit" class="btn-primary">
                            Update User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Assign Accounts Modal -->
    <div id="assign-accounts-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Assign Social Accounts</h3>
                    <button onclick="hideAssignAccountsModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <div class="mb-4">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                            <i data-lucide="megaphone" class="w-5 h-5 text-orange-600"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900" id="assign-user-name">User Name</h4>
                            <p class="text-sm text-gray-500">Marketing User</p>
                        </div>
                    </div>
                </div>

                <form id="assign-accounts-form" onsubmit="assignSocialAccounts(event)">
                    <input type="hidden" id="assign-user-id" name="user_id">

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Social Accounts</label>
                        <div class="border border-gray-300 rounded-md p-3 max-h-60 overflow-y-auto">
                            <div id="assign-social-accounts-list" class="space-y-2">
                                <!-- Social accounts will be loaded here -->
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">User will only see responses from selected accounts</p>
                    </div>

                    <div class="flex items-center justify-end space-x-3 mt-6">
                        <button type="button" onclick="hideAssignAccountsModal()" class="btn-secondary">
                            Cancel
                        </button>
                        <button type="submit" class="btn-primary">
                            Save Assignment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Company Settings Modal -->
    <div id="company-settings-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Company Settings</h3>
                    <button onclick="hideCompanySettingsModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <form id="company-settings-form" onsubmit="updateCompanySettings(event)">
                    <!-- Company Logo Section -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Company Logo</label>
                        <div class="flex items-center space-x-4">
                            <div class="relative">
                                <div id="company-logo-preview" class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                                    <i data-lucide="building" class="w-8 h-8 text-gray-400"></i>
                                </div>
                                <label for="company-logo" class="absolute -bottom-2 -right-2 bg-blue-600 text-white rounded-full p-1 cursor-pointer hover:bg-blue-700">
                                    <i data-lucide="camera" class="w-3 h-3"></i>
                                </label>
                                <input type="file" id="company-logo" name="company_logo" accept="image/*" class="hidden" onchange="previewCompanyLogo(this)">
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-600">Upload your company logo</p>
                                <p class="text-xs text-gray-500">Recommended: 64x64px, PNG or JPG</p>
                                <button type="button" onclick="removeCompanyLogo()" class="text-xs text-red-600 hover:text-red-800 mt-1">Remove Logo</button>
                            </div>
                        </div>
                    </div>

                    <!-- Company Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                            <input type="text" id="company-name" name="company_name"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="Your Company Name">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Industry</label>
                            <select id="company-industry" name="company_industry"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Select Industry</option>
                                <option value="airline">Airline</option>
                                <option value="hospitality">Hospitality</option>
                                <option value="retail">Retail</option>
                                <option value="technology">Technology</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="finance">Finance</option>
                                <option value="education">Education</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>

                    <!-- Header Customization -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Header Title</label>
                        <input type="text" id="header-title" name="header_title"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Agentic ORM" value="🤖 Agentic ORM">
                        <p class="text-xs text-gray-500 mt-1">This will appear in the main header</p>
                    </div>

                    <div class="flex items-center justify-end space-x-3 mt-6">
                        <button type="button" onclick="hideCompanySettingsModal()" class="btn-secondary">
                            Cancel
                        </button>
                        <button type="submit" class="btn-primary">
                            Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Credential Modal -->
    <div id="add-credential-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Add Secure Credentials</h3>
                    <button onclick="hideAddCredentialModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <form id="add-credential-form" onsubmit="addCredential(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Credential Name</label>
                            <input type="text" name="name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="e.g., twitter_main">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Platform</label>
                            <select name="platform" required onchange="updateCredentialFields(this.value)"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Select Platform</option>
                                <option value="twitter">Twitter</option>
                                <option value="instagram">Instagram</option>
                                <option value="facebook">Facebook</option>
                                <option value="linkedin">LinkedIn</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <input type="text" name="description"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Brief description of this credential set">
                    </div>

                    <!-- Twitter Credentials -->
                    <div id="twitter-fields" class="credential-fields hidden">
                        <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                            <i data-lucide="twitter" class="w-4 h-4 mr-2 text-blue-500"></i>
                            Twitter API Credentials
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Bearer Token</label>
                                <input type="password" name="bearer_token"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Bearer token from Twitter Developer Portal">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                                <input type="password" name="api_key"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="API Key (Consumer Key)">
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">API Secret</label>
                                <input type="password" name="api_secret"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="API Secret (Consumer Secret)">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Access Token</label>
                                <input type="password" name="access_token"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Access Token">
                            </div>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Access Token Secret</label>
                            <input type="password" name="access_token_secret"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="Access Token Secret">
                        </div>
                    </div>

                    <!-- Instagram Credentials -->
                    <div id="instagram-fields" class="credential-fields hidden">
                        <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                            <i data-lucide="instagram" class="w-4 h-4 mr-2 text-pink-500"></i>
                            Instagram API Credentials
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Access Token</label>
                                <input type="password" name="instagram_access_token"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Instagram Access Token">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">App ID</label>
                                <input type="password" name="app_id"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Instagram App ID">
                            </div>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">App Secret</label>
                            <input type="password" name="app_secret"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="Instagram App Secret">
                        </div>
                    </div>

                    <!-- Security Notice -->
                    <div class="mb-6 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i data-lucide="shield" class="w-4 h-4 text-blue-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-blue-800">
                                    <strong>Security:</strong> All credentials will be encrypted using AES-256 encryption before storage.
                                    They will never be stored in plain text.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-3 mt-6">
                        <button type="button" onclick="hideAddCredentialModal()" class="btn-secondary">
                            Cancel
                        </button>
                        <button type="submit" class="btn-primary">
                            <i data-lucide="shield-plus" class="w-4 h-4 mr-2"></i>
                            Add Credentials
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Credential Modal -->
    <div id="edit-credential-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Edit Secure Credentials</h3>
                    <button onclick="hideEditCredentialModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <form id="edit-credential-form" onsubmit="updateCredential(event)">
                    <input type="hidden" id="edit-credential-id" name="credential_id">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Credential Name</label>
                            <input type="text" id="edit-credential-name" name="name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Platform</label>
                            <select id="edit-credential-platform" name="platform" required onchange="updateEditCredentialFields(this.value)"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="twitter">Twitter</option>
                                <option value="instagram">Instagram</option>
                                <option value="facebook">Facebook</option>
                                <option value="linkedin">LinkedIn</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <input type="text" id="edit-credential-description" name="description"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Current Credentials Display -->
                    <div class="mb-4 p-3 bg-gray-50 rounded-md">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Current Credentials (Masked)</h4>
                        <div id="current-credentials-display" class="text-sm text-gray-600">
                            Loading current credentials...
                        </div>
                    </div>

                    <!-- Twitter Credentials -->
                    <div id="edit-twitter-fields" class="edit-credential-fields hidden">
                        <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                            <i data-lucide="twitter" class="w-4 h-4 mr-2 text-blue-500"></i>
                            Update Twitter API Credentials
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Bearer Token</label>
                                <input type="password" id="edit-bearer-token" name="bearer_token"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Leave empty to keep current value">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                                <input type="password" id="edit-api-key" name="api_key"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Leave empty to keep current value">
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">API Secret</label>
                                <input type="password" id="edit-api-secret" name="api_secret"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Leave empty to keep current value">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Access Token</label>
                                <input type="password" id="edit-access-token" name="access_token"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Leave empty to keep current value">
                            </div>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Access Token Secret</label>
                            <input type="password" id="edit-access-token-secret" name="access_token_secret"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="Leave empty to keep current value">
                        </div>
                    </div>

                    <!-- Instagram Credentials -->
                    <div id="edit-instagram-fields" class="edit-credential-fields hidden">
                        <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                            <i data-lucide="instagram" class="w-4 h-4 mr-2 text-pink-500"></i>
                            Update Instagram API Credentials
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Access Token</label>
                                <input type="password" id="edit-instagram-access-token" name="instagram_access_token"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Leave empty to keep current value">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">App ID</label>
                                <input type="password" id="edit-app-id" name="app_id"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Leave empty to keep current value">
                            </div>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">App Secret</label>
                            <input type="password" id="edit-app-secret" name="app_secret"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="Leave empty to keep current value">
                        </div>
                    </div>

                    <!-- Status Toggle -->
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="edit-credential-active" name="is_active" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Credential is active</span>
                        </label>
                    </div>

                    <!-- Security Notice -->
                    <div class="mb-6 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i data-lucide="shield" class="w-4 h-4 text-blue-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-blue-800">
                                    <strong>Security:</strong> Only enter new values for fields you want to update.
                                    Empty fields will keep their current encrypted values.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-3 mt-6">
                        <button type="button" onclick="hideEditCredentialModal()" class="btn-secondary">
                            Cancel
                        </button>
                        <button type="submit" class="btn-primary">
                            <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                            Update Credentials
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

                </div>
            </main>
        </div>
    </div>

    <!-- Edit Profile Modal -->
    <div id="edit-profile-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Edit Profile</h3>
                    <button onclick="hideEditProfileModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <form id="edit-profile-form" onsubmit="updateProfile(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                            <input type="text" id="edit-profile-full-name" name="full_name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="John Doe">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                            <input type="text" id="edit-profile-username" name="username" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="johndoe">
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <input type="email" id="edit-profile-email" name="email" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="<EMAIL>">
                    </div>

                    <div class="flex items-center justify-end space-x-3 mt-6">
                        <button type="button" onclick="hideEditProfileModal()" class="btn-secondary">
                            Cancel
                        </button>
                        <button type="submit" class="btn-primary">
                            <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div id="change-password-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Change Password</h3>
                    <button onclick="hideChangePasswordModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <form id="change-password-form" onsubmit="changePassword(event)">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                        <input type="password" name="current_password" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Enter current password">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                        <input type="password" name="new_password" required minlength="6"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Enter new password (min 6 characters)">
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                        <input type="password" name="confirm_password" required minlength="6"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Confirm new password">
                    </div>

                    <div class="flex items-center justify-end space-x-3">
                        <button type="button" onclick="hideChangePasswordModal()" class="btn-secondary">
                            Cancel
                        </button>
                        <button type="submit" class="btn-primary">
                            <i data-lucide="key" class="w-4 h-4 mr-2"></i>
                            Change Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // API Configuration
        const API_BASE = 'http://localhost:8001';

        // Demo data
        const demoAccounts = [
            {
                id: 1,
                platform: 'twitter',
                username: 'company_support',
                display_name: 'Company Support',
                is_active: true,
                monitor_mentions: true,
                monitor_hashtags: ['support', 'help', 'issue'],
                monitor_keywords: ['problem', 'bug', 'error'],
                follower_count: 15420,
                last_sync_at: '2024-06-03T18:30:00Z'
            },
            {
                id: 2,
                platform: 'instagram',
                username: 'brand_official',
                display_name: 'Brand Official',
                is_active: true,
                monitor_mentions: true,
                monitor_hashtags: ['brand', 'product', 'review'],
                monitor_keywords: ['love', 'amazing', 'disappointed'],
                follower_count: 45230,
                last_sync_at: '2024-06-03T17:45:00Z'
            },
            {
                id: 3,
                platform: 'twitter',
                username: 'tech_updates',
                display_name: 'Tech Updates',
                is_active: false,
                monitor_mentions: false,
                monitor_hashtags: ['tech', 'innovation'],
                monitor_keywords: ['technology'],
                follower_count: 8750,
                last_sync_at: null
            }
        ];

        const demoResponses = [
            {
                id: 1,
                original_post: {
                    content: "Your app keeps crashing on my iPhone. This is very frustrating!",
                    platform: 'twitter',
                    username: 'frustrated_user',
                    sentiment: 'negative',
                    sentiment_score: -0.85
                },
                generated_response: "Hi! We're sorry to hear about the crashes you're experiencing. Our team is actively working on a fix for iOS devices. Please DM us your device model and iOS version so we can prioritize your case. Thank you for your patience! 🛠️",
                confidence_score: 0.92,
                status: 'pending',
                created_at: '2024-06-03T18:15:00Z'
            },
            {
                id: 2,
                original_post: {
                    content: "I absolutely love the new features! Great work team! 🎉",
                    platform: 'twitter',
                    username: 'happy_customer',
                    sentiment: 'positive',
                    sentiment_score: 0.95
                },
                generated_response: "Thank you so much for the positive feedback! 🙏 We're thrilled you're enjoying the new features. Your support means the world to our team. Stay tuned for more exciting updates coming soon! ✨",
                confidence_score: 0.88,
                status: 'pending',
                created_at: '2024-06-03T18:10:00Z'
            }
        ];

        // Navigation
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.add('hidden');
            });

            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');

            // Update sidebar navigation (mobile)
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Update top navigation (desktop)
            document.querySelectorAll('.top-nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Set active state for current section
            const sidebarItem = document.querySelector(`.nav-item[data-section="${sectionId}"]`);
            const topNavItem = document.querySelector(`.top-nav-item[data-section="${sectionId}"]`);

            if (sidebarItem) {
                sidebarItem.classList.add('active');
            }
            if (topNavItem) {
                topNavItem.classList.add('active');
            }

            // Close mobile sidebar after navigation
            if (window.innerWidth <= 1024) {
                closeSidebar();
            }

            // Load section data
            if (sectionId === 'social-accounts') {
                loadRealSocialAccounts();
            } else if (sectionId === 'responses') {
                // Check if marketing user has assigned social accounts first
                if (currentUser && currentUser.role === 'marketing') {
                    loadResponses(); // This will check for assigned accounts
                } else {
                    loadRealResponses(); // Admin/infra users see real data
                }
            } else if (sectionId === 'analytics') {
                loadRealAnalytics();
            } else if (sectionId === 'my-profile') {
                loadMyProfile();
            } else if (sectionId === 'system-status') {
                // Initialize system status check
                setTimeout(() => {
                    refreshSystemStatus();
                    refreshLogs();
                }, 500);
            }
        }

        // Social Accounts
        function loadSocialAccounts() {
            const grid = document.getElementById('accounts-grid');
            grid.innerHTML = '';

            demoAccounts.forEach(account => {
                const platformIcons = {
                    twitter: 'twitter',
                    instagram: 'instagram',
                    facebook: 'facebook',
                    linkedin: 'linkedin'
                };

                const platformColors = {
                    twitter: 'bg-blue-500',
                    instagram: 'bg-pink-500',
                    facebook: 'bg-blue-600',
                    linkedin: 'bg-blue-700'
                };

                const card = document.createElement('div');
                card.className = 'card';
                card.innerHTML = `
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="p-2 rounded-lg ${platformColors[account.platform]} text-white mr-3">
                                <i data-lucide="${platformIcons[account.platform]}" class="w-5 h-5"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">@${account.username}</h3>
                                <p class="text-sm text-gray-500 capitalize">${account.platform}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="toggleAccount(${account.id})" class="p-1 rounded ${account.is_active ? 'text-green-600' : 'text-gray-400'}">
                                <i data-lucide="${account.is_active ? 'eye' : 'eye-off'}" class="w-4 h-4"></i>
                            </button>
                            <button onclick="configureAccount(${account.id})" class="p-1 text-blue-600 hover:text-blue-800">
                                <i data-lucide="settings" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Followers:</span>
                            <span class="font-medium">${account.follower_count.toLocaleString()}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Status:</span>
                            <span class="font-medium ${account.is_active ? 'text-green-600' : 'text-gray-400'}">
                                ${account.is_active ? 'Active' : 'Inactive'}
                            </span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Last Sync:</span>
                            <span class="font-medium">
                                ${account.last_sync_at ? new Date(account.last_sync_at).toLocaleDateString() : 'Never'}
                            </span>
                        </div>
                    </div>

                    <div class="border-t pt-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Monitoring</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Mentions</span>
                                <i data-lucide="${account.monitor_mentions ? 'check-circle' : 'x-circle'}" class="w-4 h-4 ${account.monitor_mentions ? 'text-green-500' : 'text-gray-400'}"></i>
                            </div>
                            <div class="text-sm">
                                <span class="text-gray-600">Hashtags: </span>
                                <span class="text-gray-900">${account.monitor_hashtags.length} configured</span>
                            </div>
                            <div class="text-sm">
                                <span class="text-gray-600">Keywords: </span>
                                <span class="text-gray-900">${account.monitor_keywords.length} configured</span>
                            </div>
                        </div>
                    </div>
                `;

                grid.appendChild(card);
            });

            // Re-initialize icons
            lucide.createIcons();
        }

        // Response Management
        function loadResponses() {
            // Check if marketing user has assigned social accounts
            if (currentUser && currentUser.role === 'marketing') {
                if (!currentUser.assigned_accounts || currentUser.assigned_accounts.length === 0) {
                    showNoSocialAccountsMessage();
                    return;
                }
            }

            const list = document.getElementById('responses-list');
            list.innerHTML = '';

            demoResponses.forEach(response => {
                const platformIcons = {
                    twitter: 'twitter',
                    instagram: 'instagram'
                };

                const sentimentColors = {
                    positive: 'bg-green-100 text-green-800',
                    negative: 'bg-red-100 text-red-800',
                    neutral: 'bg-gray-100 text-gray-800'
                };

                const card = document.createElement('div');
                card.className = 'card';
                card.innerHTML = `
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center">
                                <i data-lucide="${platformIcons[response.original_post.platform]}" class="w-5 h-5 text-gray-600 mr-2"></i>
                                <span class="font-medium text-gray-900">@${response.original_post.username}</span>
                            </div>
                            <span class="px-2 py-1 rounded-full text-xs font-medium ${sentimentColors[response.original_post.sentiment]}">
                                ${response.original_post.sentiment}
                            </span>
                            ${response.original_post.detected_language && response.original_post.detected_language !== 'en' ? `
                                <span class="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                    🌍 ${response.original_post.detected_language.toUpperCase()}
                                </span>
                            ` : ''}
                            <span class="text-sm text-gray-500">
                                ${new Date(response.created_at).toLocaleDateString()}
                            </span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                ${response.status}
                            </span>
                            <span class="text-sm text-gray-500">
                                ${Math.round(response.confidence_score * 100)}% confidence
                            </span>
                        </div>
                    </div>

                    <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-2 flex items-center">
                            <i data-lucide="message-square" class="w-4 h-4 mr-2"></i>
                            Original Post
                        </h4>
                        <p class="text-gray-700">${response.original_post.content}</p>
                        <div class="mt-2 text-sm text-gray-500">
                            Sentiment: ${response.original_post.sentiment} (${response.original_post.sentiment_score.toFixed(2)})
                        </div>
                    </div>

                    <div class="mb-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Generated Response</h4>
                        <div class="p-3 bg-blue-50 rounded-lg">
                            <p class="text-gray-800">${response.generated_response}</p>
                        </div>
                    </div>

                    ${response.status === 'pending' ? `
                    <div class="flex items-center justify-between pt-4 border-t">
                        <button onclick="editResponse(${response.id})" class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i data-lucide="edit" class="w-4 h-4 mr-1"></i>
                            Edit
                        </button>
                        <div class="flex space-x-2">
                            <button onclick="rejectResponse(${response.id})" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                                <i data-lucide="thumbs-down" class="w-4 h-4 mr-2"></i>
                                Reject
                            </button>
                            <button onclick="approveResponse(${response.id})" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                <i data-lucide="thumbs-up" class="w-4 h-4 mr-2"></i>
                                Approve
                            </button>
                        </div>
                    </div>
                    ` : ''}
                `;

                list.appendChild(card);
            });

            // Re-initialize icons
            lucide.createIcons();
        }

        // Modal functions
        function showAddAccountModal() {
            document.getElementById('add-account-modal').classList.remove('hidden');
        }

        function hideAddAccountModal() {
            document.getElementById('add-account-modal').classList.add('hidden');
        }

        // Action functions
        function toggleAccount(id) {
            alert(`Toggle account ${id} - This would connect to the API`);
        }

        function configureAccount(id) {
            alert(`Configure account ${id} - This would open the configuration panel`);
        }

        function approveResponse(id) {
            alert(`Approve response ${id} - This would call the API to approve`);
        }

        function rejectResponse(id) {
            alert(`Reject response ${id} - This would call the API to reject`);
        }

        function editResponse(id) {
            alert(`Edit response ${id} - This would open an edit modal`);
        }

        function filterResponses(status) {
            alert(`Filter responses by: ${status}`);
        }

        function refreshResponses() {
            loadRealResponses();
            loadRealDashboard();
        }

        async function testAPI() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                alert(`API Connection: ✅ Success\\nStatus: ${data.status}\\nService: ${data.service}`);
            } catch (error) {
                alert(`API Connection: ❌ Failed\\nError: ${error.message}\\n\\nMake sure the backend is running on ${API_BASE}`);
            }
        }

        // Form submission
        document.getElementById('add-account-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const accountData = Object.fromEntries(formData);
            alert(`Add account: ${JSON.stringify(accountData, null, 2)}\\n\\nThis would call the API to add the account`);
            hideAddAccountModal();
        });

        // Real data loading functions
        async function loadRealSocialAccounts() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/social-accounts/`);
                const data = await response.json();

                const accountsContainer = document.getElementById('accounts-grid');
                if (data.accounts && data.accounts.length > 0) {
                    accountsContainer.innerHTML = data.accounts.map(account => `
                        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="${account.platform === 'twitter' ? 'twitter' : 'instagram'}" class="w-5 h-5 text-blue-600"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900">@${account.username}</h3>
                                        <p class="text-sm text-gray-500">${account.display_name}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${account.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                        ${account.status}
                                    </span>
                                    <button onclick="openAccountSettings('${account.username}', '${account.platform}')" class="text-gray-400 hover:text-gray-600" title="Account Settings">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mt-4 grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">Followers:</span>
                                    <span class="font-medium">${account.followers_count.toLocaleString()}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Last Sync:</span>
                                    <span class="font-medium">${new Date(account.last_sync).toLocaleDateString()}</span>
                                </div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    accountsContainer.innerHTML = '<p class="text-gray-500">No social accounts found.</p>';
                }
                lucide.createIcons();
            } catch (error) {
                console.error('Error loading social accounts:', error);
                document.getElementById('accounts-grid').innerHTML = '<p class="text-red-500">Error loading accounts.</p>';
            }
        }

        // Infinite scroll state
        let responsesState = {
            offset: 0,
            limit: 20,
            isLoading: false,
            hasMore: true,
            searchQuery: '',
            statusFilter: '',
            sentimentFilter: ''
        };

        async function loadRealResponses(reset = false) {
            if (responsesState.isLoading || (!reset && !responsesState.hasMore)) return;

            if (reset) {
                responsesState.offset = 0;
                responsesState.hasMore = true;
                document.getElementById('responses-list').innerHTML = '';
            }

            responsesState.isLoading = true;
            showLoadingIndicator();

            try {
                // Build query parameters
                const params = new URLSearchParams({
                    limit: responsesState.limit,
                    offset: responsesState.offset
                });

                if (responsesState.searchQuery) {
                    params.append('search', responsesState.searchQuery);
                }
                if (responsesState.statusFilter) {
                    params.append('status', responsesState.statusFilter);
                }
                if (responsesState.sentimentFilter) {
                    params.append('sentiment', responsesState.sentimentFilter);
                }

                const response = await fetch(`${API_BASE}/api/v1/responses/?${params}`);
                const data = await response.json();

                // Update pagination state
                responsesState.hasMore = data.pagination?.has_more || false;
                responsesState.offset = data.pagination?.next_offset || responsesState.offset + responsesState.limit;

                // Show/hide end of results indicator
                const endOfResults = document.getElementById('end-of-results');
                if (!responsesState.hasMore && data.responses && data.responses.length > 0) {
                    endOfResults?.classList.remove('hidden');
                } else {
                    endOfResults?.classList.add('hidden');
                }

                const responsesContainer = document.getElementById('responses-list');
                if (data.responses && data.responses.length > 0) {
                    // Append new responses (don't replace existing ones)
                    const newResponsesHTML = data.responses.map(resp => `
                        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200" data-status="${resp.status}">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-3">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${resp.original_post.sentiment === 'positive' ? 'bg-green-100 text-green-800' : resp.original_post.sentiment === 'negative' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}">
                                            ${resp.original_post.sentiment}
                                        </span>
                                        <span class="text-sm text-gray-500">@${resp.original_post.author_username}</span>
                                        <span class="text-sm text-gray-500">${Math.round(resp.confidence_score * 100)}% confidence</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${resp.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : resp.status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}" data-status="${resp.status}">
                                            ${resp.status}
                                        </span>
                                    </div>

                                    <div class="mb-4">
                                        <p class="text-sm text-gray-600 mb-2"><strong>Original Post:</strong></p>
                                        <p class="text-sm bg-gray-50 p-3 rounded">${resp.original_post.content}</p>

                                        ${resp.original_post.media_urls && resp.original_post.media_urls.length > 0 ? `
                                            <div class="mt-3">
                                                <p class="text-xs text-gray-500 mb-2">📷 Attached Media:</p>
                                                <div class="grid grid-cols-2 gap-2">
                                                    ${resp.original_post.media_urls.map(url => `
                                                        <div class="relative">
                                                            <img src="${url}" alt="Tweet media" class="w-full h-24 object-cover rounded border border-gray-200 cursor-pointer hover:opacity-80 transition-opacity" onclick="openImageModal('${url}')">
                                                            <div class="absolute top-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                                                                <i data-lucide="image" class="w-3 h-3"></i>
                                                            </div>
                                                        </div>
                                                    `).join('')}
                                                </div>
                                            </div>
                                        ` : ''}

                                        <div class="mt-2 flex items-center justify-between">
                                            <p class="text-xs text-gray-500">Tweet ID: ${resp.original_post.external_id || 'N/A'}</p>
                                            ${resp.original_post.post_url ? `
                                                <a href="${resp.original_post.post_url}" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 flex items-center">
                                                    <i data-lucide="external-link" class="w-3 h-3 mr-1"></i>
                                                    View Original Tweet
                                                </a>
                                            ` : ''}
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <p class="text-sm text-gray-600 mb-2"><strong>AI Generated Response:</strong></p>
                                        <div class="text-sm bg-blue-50 p-3 rounded border-l-4 border-blue-400">
                                            <p>${resp.response_text}</p>
                                            <div class="mt-2 text-xs text-gray-600">
                                                <span>Tone: ${resp.tone}</span> •
                                                <span>Length: ${resp.response_text.length}/280 chars</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex flex-col space-y-2 ml-4">
                                    ${resp.status === 'pending' ? `
                                        <button onclick="approveResponse(${resp.id})" class="px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 flex items-center">
                                            <i data-lucide="check" class="w-3 h-3 mr-1"></i>
                                            Approve
                                        </button>
                                        <button onclick="editResponse(${resp.id}, '${resp.response_text.replace(/'/g, "\\'")}' )" class="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 flex items-center">
                                            <i data-lucide="edit" class="w-3 h-3 mr-1"></i>
                                            Edit
                                        </button>
                                        <button onclick="regenerateResponse(${resp.id}, ${resp.social_post_id || 'null'})" class="px-3 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700 flex items-center">
                                            <i data-lucide="refresh-cw" class="w-3 h-3 mr-1"></i>
                                            Regenerate
                                        </button>
                                        <button onclick="rejectResponse(${resp.id})" class="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 flex items-center">
                                            <i data-lucide="x" class="w-3 h-3 mr-1"></i>
                                            Reject
                                        </button>
                                    ` : `
                                        <span class="text-xs text-gray-500 text-center px-2 py-1 bg-gray-100 rounded">
                                            ${resp.status === 'approved' ? '✅ Approved' : '❌ Rejected'}
                                        </span>
                                        ${resp.status === 'approved' && resp.platform_response_id ? `
                                            <a href="https://twitter.com/KarthikHar25823/status/${resp.platform_response_id}" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 text-center">
                                                View Tweet
                                            </a>
                                        ` : ''}
                                    `}
                                </div>
                            </div>
                        </div>
                    `).join('');

                    responsesContainer.insertAdjacentHTML('beforeend', newResponsesHTML);
                    lucide.createIcons(); // Re-initialize icons for new content
                } else if (reset) {
                    responsesContainer.innerHTML = '<p class="text-gray-500">No responses found.</p>';
                }

                hideLoadingIndicator();
                responsesState.isLoading = false;

                // Log pagination state for debugging
                console.log('📊 Pagination state:', {
                    hasMore: responsesState.hasMore,
                    offset: responsesState.offset,
                    loaded: data.responses?.length || 0,
                    total: data.pagination?.total || 'unknown'
                });

            } catch (error) {
                console.error('Error loading responses:', error);
                hideLoadingIndicator();
                responsesState.isLoading = false;

                if (reset) {
                    document.getElementById('responses-list').innerHTML = '<p class="text-red-500">Error loading responses.</p>';
                }
            }
        }

        async function loadRealDashboard() {
            try {
                // Load responses data for dashboard
                const response = await fetch(`${API_BASE}/api/v1/responses/`);
                const data = await response.json();

                if (data.responses) {
                    // Count different statuses
                    const pending = data.responses.filter(r => r.status === 'pending').length;
                    const approved = data.responses.filter(r => r.status === 'approved').length;
                    const posted = data.responses.filter(r => r.status === 'posted').length;

                    // Count today's approvals (mock for now)
                    const approvedToday = Math.floor(Math.random() * 10) + 5; // Mock data

                    // Update dashboard stats
                    document.querySelector('[data-stat="pending-responses"]').textContent = pending;
                    document.querySelector('[data-stat="approved-today"]').textContent = approvedToday;
                    document.querySelector('[data-stat="posts-detected"]').textContent = data.responses.length;
                    document.querySelector('[data-stat="avg-response-time"]').textContent = '1.2s';

                    // Load charts
                    loadDashboardCharts(data.responses);

                    // Load recent activity
                    loadRecentActivity(data.responses);

                    console.log('✅ Enhanced dashboard data loaded:', {
                        pending, approved, posted, approvedToday, total: data.responses.length
                    });
                }
            } catch (error) {
                console.error('Error loading dashboard:', error);
                // Fallback to mock data
                loadMockDashboardData();
            }
        }

        function loadDashboardCharts(responses) {
            // Response Trends Chart
            const ctx1 = document.getElementById('responseTrendsChart');
            if (ctx1) {
                new Chart(ctx1, {
                    type: 'line',
                    data: {
                        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                        datasets: [{
                            label: 'Responses Generated',
                            data: [12, 19, 8, 15, 22, 18, 25],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: 'Responses Approved',
                            data: [8, 15, 6, 12, 18, 14, 20],
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: '#f3f4f6'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }

            // Sentiment Chart
            const ctx2 = document.getElementById('sentimentChart');
            if (ctx2) {
                const sentimentCounts = {
                    positive: responses.filter(r => r.original_post.sentiment === 'positive').length,
                    neutral: responses.filter(r => r.original_post.sentiment === 'neutral').length,
                    negative: responses.filter(r => r.original_post.sentiment === 'negative').length
                };

                new Chart(ctx2, {
                    type: 'doughnut',
                    data: {
                        labels: ['Positive', 'Neutral', 'Negative'],
                        datasets: [{
                            data: [sentimentCounts.positive, sentimentCounts.neutral, sentimentCounts.negative],
                            backgroundColor: ['#10b981', '#6b7280', '#ef4444'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }

        function loadRecentActivity(responses) {
            const activityContainer = document.getElementById('recent-activity');
            if (!activityContainer) return;

            // Get recent responses (last 5)
            const recentResponses = responses.slice(0, 5);

            const activityHTML = recentResponses.map(response => {
                const timeAgo = getTimeAgo(new Date(response.created_at));
                const statusColor = {
                    pending: 'text-yellow-600 bg-yellow-50',
                    approved: 'text-green-600 bg-green-50',
                    posted: 'text-blue-600 bg-blue-50'
                }[response.status] || 'text-gray-600 bg-gray-50';

                return `
                    <div class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg transition-colors">
                        <div class="flex-shrink-0">
                            <div class="w-2 h-2 rounded-full ${response.status === 'pending' ? 'bg-yellow-400' : response.status === 'approved' ? 'bg-green-400' : 'bg-blue-400'}"></div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm text-gray-900 truncate">
                                Response to @${response.original_post.author_username}
                            </p>
                            <p class="text-xs text-gray-500">${timeAgo}</p>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${statusColor}">
                                ${response.status}
                            </span>
                        </div>
                    </div>
                `;
            }).join('');

            activityContainer.innerHTML = activityHTML || '<p class="text-sm text-gray-500">No recent activity</p>';
        }

        function getTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            return `${Math.floor(diffInSeconds / 86400)}d ago`;
        }

        function loadMockDashboardData() {
            // Fallback mock data when API fails
            document.querySelector('[data-stat="pending-responses"]').textContent = '8';
            document.querySelector('[data-stat="approved-today"]').textContent = '12';
            document.querySelector('[data-stat="posts-detected"]').textContent = '25';
            document.querySelector('[data-stat="avg-response-time"]').textContent = '1.2s';

            // Load charts with mock data
            loadDashboardCharts([]);

            // Load mock recent activity
            const activityContainer = document.getElementById('recent-activity');
            if (activityContainer) {
                activityContainer.innerHTML = `
                    <div class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg transition-colors">
                        <div class="flex-shrink-0">
                            <div class="w-2 h-2 rounded-full bg-yellow-400"></div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm text-gray-900 truncate">Response to @customer123</p>
                            <p class="text-xs text-gray-500">2m ago</p>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium text-yellow-600 bg-yellow-50">
                                pending
                            </span>
                        </div>
                    </div>
                `;
            }
        }

        async function approveResponse(responseId) {
            try {
                // Show loading state
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i data-lucide="loader" class="w-4 h-4 mr-2 animate-spin"></i>Posting...';
                button.disabled = true;

                const response = await fetch(`${API_BASE}/api/v1/responses/${responseId}/approve`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    if (result.posted) {
                        if (result.is_simulation) {
                            showNotification(`✅ Response approved and simulated as posted! (Test tweet)`, 'success');
                        } else {
                            showNotification(`✅ Response approved and posted to Twitter!`, 'success');
                        }

                        if (result.tweet_url) {
                            showNotification(`🔗 View tweet: ${result.tweet_url}`, 'info');
                        }
                    } else {
                        const errorMsg = result.error_message || 'Unknown error';
                        showNotification(`✅ Response approved (Twitter posting failed: ${errorMsg})`, 'warning');
                    }
                    loadRealResponses(); // Reload responses
                    loadRealDashboard(); // Update stats
                } else {
                    showNotification('❌ Failed to approve response', 'error');
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            } catch (error) {
                showNotification(`❌ Error: ${error.message}`, 'error');
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        async function rejectResponse(responseId) {
            if (!confirm('Are you sure you want to reject this response?')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/v1/responses/${responseId}/reject`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('✅ Response rejected', 'success');
                    loadRealResponses();
                    loadRealDashboard();
                } else {
                    showNotification('❌ Failed to reject response', 'error');
                }
            } catch (error) {
                showNotification(`❌ Error: ${error.message}`, 'error');
            }
        }

        function editResponse(responseId, currentText) {
            const modal = document.getElementById('edit-response-modal');
            const textarea = document.getElementById('edit-response-text');
            const saveButton = document.getElementById('save-response-btn');

            textarea.value = currentText;
            modal.classList.remove('hidden');

            // Update character count
            updateCharacterCount();

            // Set up save handler
            saveButton.onclick = () => saveEditedResponse(responseId);
        }

        async function saveEditedResponse(responseId) {
            const newText = document.getElementById('edit-response-text').value.trim();

            if (!newText) {
                showNotification('Response text cannot be empty', 'error');
                return;
            }

            if (newText.length > 280) {
                showNotification('Response text is too long (max 280 characters)', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/v1/responses/${responseId}/edit`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ response_text: newText })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('✅ Response updated successfully', 'success');
                    hideEditResponseModal();
                    loadRealResponses();
                } else {
                    showNotification('❌ Failed to update response', 'error');
                }
            } catch (error) {
                showNotification(`❌ Error: ${error.message}`, 'error');
            }
        }



        function filterResponses(status) {
            const responses = document.querySelectorAll('#responses-list > div');

            responses.forEach(response => {
                const statusElement = response.querySelector('[data-status]');
                const responseStatus = statusElement ? statusElement.getAttribute('data-status') : '';

                if (status === 'all' || responseStatus === status) {
                    response.style.display = 'block';
                } else {
                    response.style.display = 'none';
                }
            });

            // Update filter indicator
            const filterSelect = document.querySelector('select[onchange="filterResponses(this.value)"]');
            const visibleCount = document.querySelectorAll('#responses-list > div[style="display: block"], #responses-list > div:not([style])').length;

            showNotification(`Showing ${visibleCount} ${status === 'all' ? '' : status} responses`, 'info');
        }

        function hideEditResponseModal() {
            document.getElementById('edit-response-modal').classList.add('hidden');
        }

        function updateCharacterCount() {
            const textarea = document.getElementById('edit-response-text');
            const counter = document.getElementById('character-count');
            const length = textarea.value.length;

            counter.textContent = `${length}/280`;
            counter.className = length > 280 ? 'text-red-600 font-medium' : length > 250 ? 'text-yellow-600' : 'text-gray-500';
        }

        // Infinite scroll and search functionality
        function setupInfiniteScroll() {
            const responsesContainer = document.getElementById('responses-list');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && responsesState.hasMore && !responsesState.isLoading) {
                        console.log('🔄 Loading more responses via infinite scroll...');
                        loadRealResponses(false); // Load more responses
                    } else if (entry.isIntersecting && !responsesState.hasMore) {
                        console.log('✅ All responses loaded - no more data available');
                        // Ensure loading indicator is hidden when no more data
                        hideLoadingIndicator();
                    }
                });
            }, { threshold: 0.1 });

            // Create a sentinel element at the bottom (remove existing first)
            const existingSentinel = document.getElementById('scroll-sentinel');
            if (existingSentinel) {
                existingSentinel.remove();
            }

            const sentinel = document.createElement('div');
            sentinel.id = 'scroll-sentinel';
            sentinel.style.height = '20px';
            sentinel.style.width = '100%';
            sentinel.style.visibility = 'hidden'; // Hidden but takes up space
            responsesContainer.parentNode.appendChild(sentinel);
            observer.observe(sentinel);
        }

        function setupSearch() {
            const searchInput = document.getElementById('response-search');
            const statusFilter = document.getElementById('status-filter');
            const sentimentFilter = document.getElementById('sentiment-filter');

            let searchTimeout;

            // Real-time search with debouncing
            searchInput?.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    responsesState.searchQuery = e.target.value.trim();
                    loadRealResponses(true); // Reset and search
                }, 300);
            });

            // Filter handlers
            statusFilter?.addEventListener('change', (e) => {
                responsesState.statusFilter = e.target.value;
                loadRealResponses(true);
            });

            sentimentFilter?.addEventListener('change', (e) => {
                responsesState.sentimentFilter = e.target.value;
                loadRealResponses(true);
            });
        }

        function showLoadingIndicator() {
            const indicator = document.getElementById('loading-indicator');
            if (indicator) {
                indicator.classList.remove('hidden');
            }
        }

        function hideLoadingIndicator() {
            const indicator = document.getElementById('loading-indicator');
            if (indicator) {
                // Add smooth fade out
                indicator.style.opacity = '0';
                setTimeout(() => {
                    indicator.classList.add('hidden');
                    indicator.style.opacity = '1';
                }, 300);
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const colors = {
                success: 'bg-green-100 border-green-400 text-green-700',
                error: 'bg-red-100 border-red-400 text-red-700',
                warning: 'bg-yellow-100 border-yellow-400 text-yellow-700',
                info: 'bg-blue-100 border-blue-400 text-blue-700'
            };

            notification.className = `fixed top-4 right-4 p-4 border-l-4 rounded shadow-lg z-50 max-w-md ${colors[type]}`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-1">${message}</div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-lg font-bold">&times;</button>
                </div>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Real Analytics Functions
        async function loadRealAnalytics() {
            try {
                // Load all analytics data
                await Promise.all([
                    loadSentimentAnalytics(),
                    loadPlatformAnalytics(),
                    loadTimelineAnalytics()
                ]);

                showNotification('Analytics data refreshed', 'success');
            } catch (error) {
                console.error('Error loading analytics:', error);
                showNotification('Failed to load analytics data', 'error');
            }
        }

        async function loadSentimentAnalytics() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/analytics/sentiment`);
                const data = await response.json();

                // Update sentiment distribution
                const sentimentData = data.sentiment_distribution;

                // Update the sentiment chart data
                document.querySelector('[data-chart="sentiment-positive"]').textContent = `${sentimentData.positive.percentage}%`;
                document.querySelector('[data-chart="sentiment-negative"]').textContent = `${sentimentData.negative.percentage}%`;
                document.querySelector('[data-chart="sentiment-neutral"]').textContent = `${sentimentData.neutral.percentage}%`;

                // Update sentiment counts
                document.querySelector('[data-stat="sentiment-positive-count"]').textContent = sentimentData.positive.count;
                document.querySelector('[data-stat="sentiment-negative-count"]').textContent = sentimentData.negative.count;
                document.querySelector('[data-stat="sentiment-neutral-count"]').textContent = sentimentData.neutral.count;

                // Update progress bars
                const positiveBar = document.querySelector('[data-bar="sentiment-positive"]');
                const neutralBar = document.querySelector('[data-bar="sentiment-neutral"]');
                const negativeBar = document.querySelector('[data-bar="sentiment-negative"]');

                if (positiveBar) positiveBar.style.width = `${sentimentData.positive.percentage}%`;
                if (neutralBar) neutralBar.style.width = `${sentimentData.neutral.percentage}%`;
                if (negativeBar) negativeBar.style.width = `${sentimentData.negative.percentage}%`;

                console.log('✅ Sentiment analytics loaded:', sentimentData);
            } catch (error) {
                console.error('Error loading sentiment analytics:', error);
                showNotification('Failed to load sentiment analytics', 'error');
            }
        }

        async function loadPlatformAnalytics() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/analytics/platform`);
                const data = await response.json();

                // Update platform performance data
                const platformData = data.platform_performance;

                // Update Twitter stats (main platform)
                if (platformData.twitter) {
                    const twitterPostsEl = document.querySelector('[data-stat="twitter-posts"]');
                    const twitterResponsesEl = document.querySelector('[data-stat="twitter-responses"]');

                    if (twitterPostsEl) twitterPostsEl.textContent = platformData.twitter.posts;
                    if (twitterResponsesEl) twitterResponsesEl.textContent = platformData.twitter.responses;
                }

                console.log('✅ Platform analytics loaded:', platformData);
            } catch (error) {
                console.error('Error loading platform analytics:', error);
                showNotification('Failed to load platform analytics', 'error');
            }
        }

        async function loadTimelineAnalytics() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/analytics/timeline`);
                const data = await response.json();

                // Update timeline data
                const timelineData = data.timeline;

                // Calculate totals for the week
                const totalPosts = timelineData.reduce((sum, day) => sum + day.posts, 0);
                const totalPositive = timelineData.reduce((sum, day) => sum + day.positive, 0);
                const totalNegative = timelineData.reduce((sum, day) => sum + day.negative, 0);

                // Update weekly stats
                const weeklyPostsEls = document.querySelectorAll('[data-stat="weekly-posts"]');
                const weeklyPositiveEl = document.querySelector('[data-stat="weekly-positive"]');
                const weeklyNegativeEl = document.querySelector('[data-stat="weekly-negative"]');
                const todayPostsEl = document.querySelector('[data-stat="today-posts"]');

                weeklyPostsEls.forEach(el => el.textContent = totalPosts);
                if (weeklyPositiveEl) weeklyPositiveEl.textContent = totalPositive;
                if (weeklyNegativeEl) weeklyNegativeEl.textContent = totalNegative;

                // Update last 7 days activity
                const lastDay = timelineData[timelineData.length - 1];
                if (lastDay && todayPostsEl) {
                    todayPostsEl.textContent = lastDay.posts;
                }

                console.log('✅ Timeline analytics loaded:', {totalPosts, totalPositive, totalNegative});
            } catch (error) {
                console.error('Error loading timeline analytics:', error);
                showNotification('Failed to load timeline analytics', 'error');
            }
        }

        // Account Settings Functions
        function openAccountSettings(username, platform) {
            // Update modal content
            document.getElementById('settings-account-name').textContent = `@${username}`;
            document.getElementById('settings-platform-name').textContent = platform.charAt(0).toUpperCase() + platform.slice(1);

            // Update platform icon
            const iconElement = document.getElementById('settings-platform-icon');
            iconElement.setAttribute('data-lucide', platform === 'twitter' ? 'twitter' : 'instagram');

            // Show modal
            document.getElementById('account-settings-modal').classList.remove('hidden');

            // Re-initialize icons
            lucide.createIcons();

            // Load current settings (if available)
            loadAccountSettings(username, platform);
        }

        function hideAccountSettingsModal() {
            document.getElementById('account-settings-modal').classList.add('hidden');
        }

        async function loadAccountSettings(username, platform) {
            try {
                console.log(`Loading settings for @${username} on ${platform}`);

                const response = await fetch(`${API_BASE}/api/v1/social-accounts/${username}/settings`);

                if (response.ok) {
                    const settings = await response.json();

                    // Populate form with current settings
                    document.getElementById('monitor-mentions').checked = settings.monitor_mentions;
                    document.getElementById('auto-generate').checked = settings.auto_generate;
                    document.getElementById('realtime-monitoring').checked = settings.realtime_monitoring;
                    document.getElementById('response-tone').value = settings.response_tone;
                    document.getElementById('confidence-threshold').value = settings.confidence_threshold;
                    document.getElementById('time-window-hours').value = settings.time_window_hours !== undefined ? settings.time_window_hours : 2;
                    document.getElementById('monitor-keywords').value = settings.monitor_keywords.join(', ');
                    document.getElementById('monitor-hashtags').value = settings.monitor_hashtags.join(', ');

                    console.log('✅ Settings loaded:', settings);
                } else {
                    console.log('Using default settings (account not found or no settings saved)');
                }

                // Update confidence slider display
                updateConfidenceDisplay();
            } catch (error) {
                console.error('Error loading account settings:', error);
                showNotification('Failed to load account settings', 'warning');
            }
        }

        async function saveAccountSettings() {
            try {
                const username = document.getElementById('settings-account-name').textContent.replace('@', '');
                const platform = document.getElementById('settings-platform-name').textContent.toLowerCase();

                const settings = {
                    monitor_mentions: document.getElementById('monitor-mentions').checked,
                    auto_generate: document.getElementById('auto-generate').checked,
                    realtime_monitoring: document.getElementById('realtime-monitoring').checked,
                    response_tone: document.getElementById('response-tone').value,
                    confidence_threshold: parseFloat(document.getElementById('confidence-threshold').value),
                    time_window_hours: parseInt(document.getElementById('time-window-hours').value),
                    monitor_keywords: document.getElementById('monitor-keywords').value.split(',').map(k => k.trim()).filter(k => k),
                    monitor_hashtags: document.getElementById('monitor-hashtags').value.split(',').map(h => h.trim()).filter(h => h)
                };

                console.log(`Saving settings for @${username}:`, settings);

                const response = await fetch(`${API_BASE}/api/v1/social-accounts/${username}/settings`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(settings)
                });

                if (response.ok) {
                    showNotification(`✅ Settings saved for @${username}`, 'success');

                    // Show what was saved
                    if (settings.monitor_keywords.length > 0) {
                        showNotification(`📝 Keywords: ${settings.monitor_keywords.join(', ')}`, 'info');
                    }
                    if (settings.monitor_hashtags.length > 0) {
                        showNotification(`🏷️ Hashtags: ${settings.monitor_hashtags.join(', ')}`, 'info');
                    }

                    hideAccountSettingsModal();
                    loadRealSocialAccounts();
                } else {
                    const error = await response.json();
                    showNotification(`Failed to save settings: ${error.error || 'Unknown error'}`, 'error');
                }

            } catch (error) {
                console.error('Error saving account settings:', error);
                showNotification('Failed to save settings', 'error');
            }
        }

        function updateConfidenceDisplay() {
            const slider = document.getElementById('confidence-threshold');
            const display = document.getElementById('confidence-value');

            slider.addEventListener('input', function() {
                const value = Math.round(this.value * 100);
                display.textContent = `${value}%`;
            });
        }

        // Monitoring Control Functions
        let monitoringEnabled = true;

        async function loadMonitoringStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/monitoring/status`);
                const data = await response.json();

                monitoringEnabled = data.enabled;
                updateMonitoringUI(data);

                console.log('✅ Monitoring status loaded:', data);
            } catch (error) {
                console.error('Error loading monitoring status:', error);
            }
        }

        function updateMonitoringUI(status) {
            const toggle = document.getElementById('monitoring-toggle');
            const icon = document.getElementById('monitoring-icon');
            const text = document.getElementById('monitoring-text');
            const statusBar = document.getElementById('monitoring-status');
            const statusText = document.getElementById('monitoring-status-text');
            const lastTrigger = document.getElementById('monitoring-last-trigger');

            if (status.enabled) {
                toggle.className = 'flex items-center space-x-2 px-3 py-1 rounded-md text-sm font-medium transition-colors bg-green-100 text-green-800 hover:bg-green-200';
                icon.setAttribute('data-lucide', 'play');
                text.textContent = 'Enabled';
                statusText.textContent = 'Monitoring is active - checking for mentions automatically';
                statusBar.className = 'mb-6 p-4 bg-green-50 border border-green-200 rounded-lg';
            } else {
                toggle.className = 'flex items-center space-x-2 px-3 py-1 rounded-md text-sm font-medium transition-colors bg-red-100 text-red-800 hover:bg-red-200';
                icon.setAttribute('data-lucide', 'pause');
                text.textContent = 'Disabled';
                statusText.textContent = 'Monitoring is paused - use Manual Check to trigger once';
                statusBar.className = 'mb-6 p-4 bg-red-50 border border-red-200 rounded-lg';
            }

            // Update last trigger info
            if (status.last_manual_trigger) {
                const date = new Date(status.last_manual_trigger);
                lastTrigger.textContent = `Last manual trigger: ${date.toLocaleString()} (${status.total_manual_triggers} total)`;
            } else {
                lastTrigger.textContent = 'Last manual trigger: Never';
            }

            // Show status bar
            statusBar.classList.remove('hidden');

            // Re-initialize icons
            lucide.createIcons();
        }

        async function toggleMonitoring() {
            try {
                const newState = !monitoringEnabled;

                const response = await fetch(`${API_BASE}/api/v1/monitoring/toggle`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ enabled: newState })
                });

                const data = await response.json();

                if (data.success) {
                    monitoringEnabled = data.enabled;
                    updateMonitoringUI(data);

                    const action = data.enabled ? 'enabled' : 'disabled';
                    showNotification(`Monitoring ${action}`, 'success');

                    console.log(`✅ Monitoring ${action}`);
                } else {
                    showNotification('Failed to toggle monitoring', 'error');
                }
            } catch (error) {
                console.error('Error toggling monitoring:', error);
                showNotification('Failed to toggle monitoring', 'error');
            }
        }

        async function triggerManualMonitoring() {
            try {
                showNotification('Triggering manual monitoring check...', 'info');

                const response = await fetch(`${API_BASE}/api/v1/monitoring/trigger`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    showNotification(`Manual check completed! Found ${data.new_mentions} new mentions`, 'success');

                    // Refresh monitoring status
                    await loadMonitoringStatus();

                    // Refresh responses if new mentions found
                    if (data.new_mentions > 0) {
                        await loadRealResponses();
                        await loadRealDashboard();
                    }

                    console.log('✅ Manual monitoring completed:', data);
                } else {
                    showNotification(`Manual check failed: ${data.message}`, 'error');
                }
            } catch (error) {
                console.error('Error triggering manual monitoring:', error);
                showNotification('Manual monitoring check failed', 'error');
            }
        }

        // Model Management Functions
        let currentModelStatus = null;
        let updateCheckInProgress = false;

        async function loadModelStatus() {
            try {
                // Mock model status data for now
                const data = {
                    model_name: 'Microsoft Phi-3 Mini',
                    model_type: 'Small Language Model (SLM)',
                    license: 'MIT License',
                    privacy_status: '🔒 100% Local',
                    data_security: '✅ No Data Sent to Cloud',
                    current_version: {
                        sha: 'a4f4c5d8e9f2b3c7d1e6f8a9b2c5d8e1',
                        last_modified: new Date().toISOString()
                    },
                    status: 'healthy',
                    memory_usage: '2.1 GB',
                    response_time: '1.2s avg',
                    uptime: '24h 15m'
                };

                currentModelStatus = data;
                updateModelUI(data);

                console.log('✅ Model status loaded:', data);
            } catch (error) {
                console.error('Error loading model status:', error);
                showNotification('Failed to load model status', 'error');
            }
        }

        function updateModelUI(status) {
            // Update model information
            document.getElementById('model-name').textContent = status.model_name || 'Unknown';
            document.getElementById('model-type').textContent = status.model_type || 'Unknown';
            document.getElementById('model-license').textContent = status.license || 'Unknown';
            document.getElementById('privacy-status').textContent = status.privacy_status || '🔒 100% Local';
            document.getElementById('data-security').textContent = status.data_security || '✅ None Used';

            // Update version info
            if (status.current_version && status.current_version.sha) {
                document.getElementById('model-version').textContent = status.current_version.sha.substring(0, 8) + '...';

                if (status.current_version.last_modified) {
                    const date = new Date(status.current_version.last_modified);
                    document.getElementById('model-last-updated').textContent = date.toLocaleDateString();
                } else {
                    document.getElementById('model-last-updated').textContent = 'Unknown';
                }
            } else {
                document.getElementById('model-version').textContent = 'Not available';
                document.getElementById('model-last-updated').textContent = 'Unknown';
            }

            // Update history
            updateHistoryUI(status.update_history || []);
        }

        function updateHistoryUI(history) {
            const historyContainer = document.getElementById('update-history');

            if (!history || history.length === 0) {
                historyContainer.innerHTML = `
                    <div class="text-sm text-gray-500 text-center py-4">
                        No update history available
                    </div>
                `;
                return;
            }

            const historyHTML = history.slice(-5).reverse().map(update => {
                const date = new Date(update.timestamp);
                const fromVersion = update.from_version ? update.from_version.substring(0, 8) + '...' : 'Unknown';
                const toVersion = update.to_version ? update.to_version.substring(0, 8) + '...' : 'Unknown';

                return `
                    <div class="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                        <div>
                            <div class="text-sm font-medium text-gray-900">
                                ${fromVersion} → ${toVersion}
                            </div>
                            <div class="text-xs text-gray-500">
                                ${date.toLocaleDateString()} ${date.toLocaleTimeString()}
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            ${update.backup_created ?
                                '<span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Backup Created</span>' :
                                '<span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">No Backup</span>'
                            }
                        </div>
                    </div>
                `;
            }).join('');

            historyContainer.innerHTML = historyHTML;
        }

        async function checkModelUpdates() {
            if (updateCheckInProgress) {
                showNotification('Update check already in progress', 'info');
                return;
            }

            try {
                updateCheckInProgress = true;
                showNotification('Checking for model updates...', 'info');

                const response = await fetch(`${API_BASE}/api/v1/model/check-updates`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.error) {
                    showNotification(`Update check failed: ${data.error}`, 'error');
                    return;
                }

                if (data.update_available) {
                    showUpdateAvailable(data);
                    showNotification('New model version available!', 'success');
                } else {
                    showNotification('Model is up to date', 'success');
                    hideUpdateCard();
                }

                console.log('✅ Update check completed:', data);

            } catch (error) {
                console.error('Error checking for updates:', error);
                showNotification('Failed to check for updates', 'error');
            } finally {
                updateCheckInProgress = false;
            }
        }

        function showUpdateAvailable(updateData) {
            const updateCard = document.getElementById('update-status-card');
            const updateMessage = document.getElementById('update-message');
            const updateDetails = document.getElementById('update-details');
            const currentVersionShort = document.getElementById('current-version-short');
            const latestVersionShort = document.getElementById('latest-version-short');

            // Show update card
            updateCard.classList.remove('hidden');

            // Update content
            updateMessage.textContent = 'A new version of Phi-3 Mini is available';

            const securityStatus = updateData.security_verified ? 'Passed' : 'Failed';
            const securityColor = updateData.security_verified ? 'text-green-600' : 'text-red-600';
            updateDetails.innerHTML = `Security verification: <span class="${securityColor}">${securityStatus}</span>`;

            // Update version info
            if (updateData.current_version && updateData.current_version.sha) {
                currentVersionShort.textContent = updateData.current_version.sha.substring(0, 8) + '...';
            } else {
                currentVersionShort.textContent = 'Unknown';
            }

            if (updateData.latest_version && updateData.latest_version.sha) {
                latestVersionShort.textContent = updateData.latest_version.sha.substring(0, 8) + '...';
            } else {
                latestVersionShort.textContent = 'Unknown';
            }

            // Store update data for later use
            window.pendingUpdate = updateData;
        }

        function hideUpdateCard() {
            document.getElementById('update-status-card').classList.add('hidden');
            window.pendingUpdate = null;
        }

        function dismissUpdate() {
            hideUpdateCard();
            showNotification('Update dismissed', 'info');
        }

        async function updateModel() {
            if (!window.pendingUpdate) {
                showNotification('No pending update available', 'error');
                return;
            }

            if (!window.pendingUpdate.security_verified) {
                const confirmed = confirm(
                    'Security verification failed for this update. ' +
                    'Are you sure you want to proceed? This could be a security risk.'
                );
                if (!confirmed) {
                    return;
                }
            }

            const confirmed = confirm(
                'This will update the Phi-3 Mini model. ' +
                'A backup will be created automatically. ' +
                'The system may need to restart after the update. ' +
                'Do you want to proceed?'
            );

            if (!confirmed) {
                return;
            }

            try {
                showNotification('Starting model update...', 'info');

                const response = await fetch(`${API_BASE}/api/v1/model/update`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        force: !window.pendingUpdate.security_verified
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showNotification('Model updated successfully!', 'success');
                    hideUpdateCard();

                    // Reload model status
                    await loadModelStatus();

                    if (data.restart_required) {
                        showNotification('System restart may be required for changes to take effect', 'info');
                    }
                } else {
                    showNotification(`Update failed: ${data.message}`, 'error');
                }

                console.log('✅ Model update completed:', data);

            } catch (error) {
                console.error('Error updating model:', error);
                showNotification('Model update failed', 'error');
            }
        }

        // Advanced Filtering and Search Functions
        let allResponses = [];
        let filteredResponses = [];
        let currentPage = 1;
        let responsesPerPage = 10;
        let searchTimeout = null;
        let realTimeSearchTimeout = null;
        let isDataLoaded = false;
        let currentView = 'list'; // 'list' or 'grid'

        // Real-time search as user types
        function handleRealTimeSearch(event) {
            const searchInput = event.target;
            const searchTerm = searchInput.value.trim();

            // Show loading indicator
            const loadingIndicator = document.getElementById('search-loading');
            if (searchTerm.length > 0) {
                loadingIndicator.classList.remove('hidden');
            }

            // Clear previous timeout
            clearTimeout(realTimeSearchTimeout);

            // Set new timeout for real-time search
            realTimeSearchTimeout = setTimeout(() => {
                // Ensure data is loaded before filtering
                if (!isDataLoaded || !Array.isArray(allResponses)) {
                    console.log('Data not ready for filtering, loading...');
                    loadResponsesForFiltering().then(() => {
                        applyFilters();
                        loadingIndicator.classList.add('hidden');
                    });
                } else {
                    applyFilters();
                    loadingIndicator.classList.add('hidden');
                }
            }, 150); // Faster response for real-time feel
        }

        function handleSearchInput(event) {
            // Handle Enter key for immediate search
            if (event.key === 'Enter') {
                clearTimeout(realTimeSearchTimeout);
                applyFilters();
                document.getElementById('search-loading').classList.add('hidden');
            }
        }

        // Toggle advanced filters visibility
        function toggleAdvancedFilters() {
            const filtersDiv = document.getElementById('advanced-filters');
            const toggleBtn = document.getElementById('toggle-filters-btn');
            const toggleText = document.getElementById('toggle-filters-text');
            const toggleIcon = document.getElementById('toggle-filters-icon');

            if (filtersDiv.classList.contains('hidden')) {
                // Show filters
                filtersDiv.classList.remove('hidden');
                filtersDiv.style.maxHeight = 'none';
                filtersDiv.style.opacity = '1';
                toggleText.textContent = 'Hide Filters';
                toggleIcon.style.transform = 'rotate(180deg)';
            } else {
                // Hide filters
                filtersDiv.classList.add('hidden');
                toggleText.textContent = 'Show Filters';
                toggleIcon.style.transform = 'rotate(0deg)';
            }
        }

        // Load responses data for filtering
        async function loadResponsesForFiltering() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/responses/`);
                if (response.ok) {
                    const data = await response.json();

                    // Handle different response formats
                    let responses = [];
                    if (Array.isArray(data)) {
                        responses = data;
                    } else if (data.responses && Array.isArray(data.responses)) {
                        responses = data.responses;
                    } else if (data.data && Array.isArray(data.data)) {
                        responses = data.data;
                    } else {
                        console.warn('Unexpected response format, using empty array:', data);
                        responses = [];
                    }

                    allResponses = responses;
                    filteredResponses = [...allResponses];
                    isDataLoaded = true;
                    console.log(`Loaded ${allResponses.length} responses for filtering`);
                } else {
                    console.error('Failed to load responses:', response.status);
                    allResponses = [];
                    filteredResponses = [];
                    isDataLoaded = false;
                }
            } catch (error) {
                console.error('Error loading responses for filtering:', error);
                allResponses = [];
                filteredResponses = [];
                isDataLoaded = false;
            }
        }

        function applyFilters() {
            // Ensure we have valid data
            if (!Array.isArray(allResponses)) {
                console.log('allResponses is not an array, initializing...');
                allResponses = [];
                filteredResponses = [];
                if (!isDataLoaded) {
                    loadResponsesForFiltering().then(() => {
                        if (allResponses.length > 0) {
                            applyFilters();
                        }
                    });
                    return;
                }
            }

            const searchTerm = document.getElementById('search-input')?.value?.toLowerCase() || '';
            const statusFilter = document.getElementById('status-filter')?.value || '';
            const sentimentFilter = document.getElementById('sentiment-filter')?.value || '';
            const platformFilter = document.getElementById('platform-filter')?.value || '';
            const priorityFilter = document.getElementById('priority-filter')?.value || '';
            const dateFilter = document.getElementById('date-filter')?.value || '';
            const responseTimeFilter = document.getElementById('response-time-filter')?.value || '';
            const confidenceFilter = document.getElementById('confidence-filter')?.value || '';
            const sortBy = document.getElementById('sort-filter')?.value || 'newest';

            // Apply filters with error handling
            try {
                filteredResponses = allResponses.filter(response => {
                // Enhanced search filter - search across multiple fields
                if (searchTerm) {
                    const searchableFields = [
                        // Post content and response
                        response.original_post?.content || '',
                        response.response_text || '',

                        // Author information
                        response.original_post?.author_username || '',
                        response.original_post?.author_display_name || '',

                        // IDs and technical fields
                        response.id?.toString() || '',
                        response.social_post_id?.toString() || '',
                        response.original_post?.external_id || '',

                        // Status and sentiment
                        response.status || '',
                        response.original_post?.sentiment || '',
                        response.tone || '',

                        // Platform
                        response.original_post?.platform || '',

                        // Dates (formatted for search)
                        response.created_at ? new Date(response.created_at).toLocaleDateString() : '',
                        response.created_at ? new Date(response.created_at).toLocaleString() : '',
                        response.original_post?.posted_at ? new Date(response.original_post.posted_at).toLocaleDateString() : '',

                        // Confidence score
                        response.confidence?.toString() || '',

                        // Additional searchable terms
                        response.approved_by || '',
                        response.platform_response_id || ''
                    ];

                    const searchableText = searchableFields.join(' ').toLowerCase();

                    // Support multiple search terms (space-separated)
                    const searchTerms = searchTerm.toLowerCase().split(' ').filter(term => term.length > 0);
                    const matchesAllTerms = searchTerms.every(term => searchableText.includes(term));

                    if (!matchesAllTerms) {
                        return false;
                    }
                }

                // Status filter
                if (statusFilter && response.status !== statusFilter) {
                    return false;
                }

                // Sentiment filter
                if (sentimentFilter && response.original_post?.sentiment !== sentimentFilter) {
                    return false;
                }

                // Platform filter
                if (platformFilter && response.original_post?.platform !== platformFilter) {
                    return false;
                }

                // Priority filter (based on sentiment and keywords)
                if (priorityFilter) {
                    const priority = calculatePriority(response);
                    if (priority !== priorityFilter) {
                        return false;
                    }
                }

                // Date filter
                if (dateFilter && !matchesDateFilter(response.created_at, dateFilter)) {
                    return false;
                }

                // Response time filter
                if (responseTimeFilter && !matchesResponseTimeFilter(response, responseTimeFilter)) {
                    return false;
                }

                // Confidence filter
                if (confidenceFilter && !matchesConfidenceFilter(response.confidence, confidenceFilter)) {
                    return false;
                }

                return true;
            });

            // Sort results
            sortResponses(filteredResponses, sortBy);

            // Update UI
            updateFilteredResults();
            updateActiveFilters();
            displayResponses();

        } catch (error) {
            console.error('Error in applyFilters:', error);
            // Fallback to empty results
            filteredResponses = [];
            updateFilteredResults();
            displayResponses();
        }
    }

        function calculatePriority(response) {
            const content = (response.original_post?.content || '').toLowerCase();
            const sentiment = response.original_post?.sentiment;

            // High priority keywords
            const urgentKeywords = ['urgent', 'emergency', 'stranded', 'missed connection', 'cancelled', 'delayed', 'refund', 'complaint'];
            const hasUrgentKeyword = urgentKeywords.some(keyword => content.includes(keyword));

            if (sentiment === 'negative' && hasUrgentKeyword) return 'high';
            if (sentiment === 'negative' || hasUrgentKeyword) return 'medium';
            return 'low';
        }

        function matchesDateFilter(dateString, filter) {
            const date = new Date(dateString);
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            const last7days = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const last30days = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

            switch (filter) {
                case 'today':
                    return date >= today;
                case 'yesterday':
                    return date >= yesterday && date < today;
                case 'last7days':
                    return date >= last7days;
                case 'last30days':
                    return date >= last30days;
                default:
                    return true;
            }
        }

        function matchesResponseTimeFilter(response, filter) {
            if (!response.created_at || !response.original_post?.posted_at) return true;

            const responseTime = new Date(response.created_at);
            const postTime = new Date(response.original_post.posted_at);
            const diffHours = (responseTime - postTime) / (1000 * 60 * 60);

            switch (filter) {
                case 'under1h':
                    return diffHours < 1;
                case '1to4h':
                    return diffHours >= 1 && diffHours < 4;
                case '4to24h':
                    return diffHours >= 4 && diffHours < 24;
                case 'over24h':
                    return diffHours >= 24;
                default:
                    return true;
            }
        }

        function matchesConfidenceFilter(confidence, filter) {
            const conf = parseFloat(confidence) || 0;

            switch (filter) {
                case 'high':
                    return conf >= 0.9;
                case 'medium':
                    return conf >= 0.7 && conf < 0.9;
                case 'low':
                    return conf >= 0.5 && conf < 0.7;
                case 'very-low':
                    return conf < 0.5;
                default:
                    return true;
            }
        }

        function sortResponses(responses, sortBy) {
            responses.sort((a, b) => {
                switch (sortBy) {
                    case 'newest':
                        return new Date(b.created_at) - new Date(a.created_at);
                    case 'oldest':
                        return new Date(a.created_at) - new Date(b.created_at);
                    case 'priority':
                        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
                        return priorityOrder[calculatePriority(b)] - priorityOrder[calculatePriority(a)];
                    case 'sentiment':
                        const sentimentOrder = { 'negative': 3, 'neutral': 2, 'positive': 1 };
                        return sentimentOrder[b.original_post?.sentiment] - sentimentOrder[a.original_post?.sentiment];
                    case 'confidence':
                        return (parseFloat(b.confidence) || 0) - (parseFloat(a.confidence) || 0);
                    case 'response-time':
                        const aTime = a.created_at && a.original_post?.posted_at ?
                            new Date(a.created_at) - new Date(a.original_post.posted_at) : 0;
                        const bTime = b.created_at && b.original_post?.posted_at ?
                            new Date(b.created_at) - new Date(b.original_post.posted_at) : 0;
                        return aTime - bTime;
                    default:
                        return new Date(b.created_at) - new Date(a.created_at);
                }
            });
        }

        function applyQuickFilter(type) {
            // Clear existing filters
            clearAllFilters();

            const searchInput = document.getElementById('search-input');
            const sentimentFilter = document.getElementById('sentiment-filter');
            const priorityFilter = document.getElementById('priority-filter');

            switch (type) {
                case 'pending':
                    const statusFilter = document.getElementById('status-filter');
                    statusFilter.value = 'pending';
                    break;
                case 'urgent':
                    searchInput.value = 'urgent emergency stranded';
                    priorityFilter.value = 'high';
                    break;
                case 'complaints':
                    sentimentFilter.value = 'negative';
                    break;
                case 'delays':
                    searchInput.value = 'delay delayed flight';
                    break;
                case 'baggage':
                    searchInput.value = 'baggage luggage lost';
                    break;
                case 'refunds':
                    searchInput.value = 'refund money compensation';
                    break;
                case 'praise':
                    sentimentFilter.value = 'positive';
                    break;
            }

            // Highlight active quick filter
            document.querySelectorAll('.quick-filter-tag').forEach(tag => {
                tag.classList.remove('active');
            });
            event.target.classList.add('active');

            applyFilters();
        }

        function clearAllFilters() {
            // Clear search input
            const searchInput = document.getElementById('search-input');
            if (searchInput) searchInput.value = '';

            // Clear filter dropdowns
            const filters = [
                'status-filter', 'sentiment-filter', 'platform-filter', 'priority-filter',
                'date-filter', 'response-time-filter', 'confidence-filter'
            ];

            filters.forEach(filterId => {
                const element = document.getElementById(filterId);
                if (element) element.value = '';
            });

            // Reset sort filter
            const sortFilter = document.getElementById('sort-filter');
            if (sortFilter) sortFilter.value = 'newest';

            // Clear quick filter highlights
            document.querySelectorAll('.quick-filter-tag').forEach(tag => {
                tag.classList.remove('active');
            });

            applyFilters();
        }

        function updateFilteredResults() {
            const resultsCount = document.getElementById('results-count');
            const filteredCount = document.getElementById('filtered-count');
            const totalCount = document.getElementById('total-count');

            resultsCount.textContent = filteredResponses.length;
            totalCount.textContent = allResponses.length;

            if (filteredResponses.length < allResponses.length) {
                filteredCount.classList.remove('hidden');
            } else {
                filteredCount.classList.add('hidden');
            }
        }

        function updateActiveFilters() {
            const activeFiltersContainer = document.getElementById('active-filters');
            const activeFiltersList = document.getElementById('active-filters-list');

            const filters = [];

            // Check each filter
            const searchTerm = document.getElementById('search-input').value;
            if (searchTerm) filters.push({ type: 'search', value: searchTerm, label: `Search: "${searchTerm}"` });

            const statusFilter = document.getElementById('status-filter').value;
            if (statusFilter) filters.push({ type: 'status', value: statusFilter, label: `Status: ${statusFilter}` });

            const sentimentFilter = document.getElementById('sentiment-filter').value;
            if (sentimentFilter) filters.push({ type: 'sentiment', value: sentimentFilter, label: `Sentiment: ${sentimentFilter}` });

            // Add more filters as needed...

            if (filters.length > 0) {
                activeFiltersContainer.classList.remove('hidden');
                activeFiltersList.innerHTML = filters.map(filter =>
                    `<span class="filter-badge">
                        ${filter.label}
                        <button onclick="removeFilter('${filter.type}', '${filter.value}')" class="ml-1 hover:opacity-100">×</button>
                    </span>`
                ).join('');
            } else {
                activeFiltersContainer.classList.add('hidden');
            }
        }

        function removeFilter(type, value) {
            switch (type) {
                case 'search':
                    document.getElementById('search-input').value = '';
                    break;
                case 'status':
                    document.getElementById('status-filter').value = '';
                    break;
                case 'sentiment':
                    document.getElementById('sentiment-filter').value = '';
                    break;
                // Add more cases as needed
            }
            applyFilters();
        }

        function displayResponses() {
            // For infinite scroll, show all filtered responses (no pagination)
            const pageResponses = filteredResponses;

            // Get current search term for highlighting
            const searchTerm = document.getElementById('search-input').value.trim();

            // Display responses with search highlighting
            const responsesList = document.getElementById('responses-list');

            // Add loading state to prevent flashing
            responsesList.classList.remove('loaded');

            // Small delay to prevent flashing
            setTimeout(() => {
                if (pageResponses.length === 0) {
                    const searchMessage = searchTerm ?
                        `No responses found for "${searchTerm}"` :
                        'No responses found';

                    responsesList.innerHTML = `
                        <div class="text-center py-8">
                            <i data-lucide="search" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">${searchMessage}</h3>
                            <p class="text-gray-500">Try adjusting your filters or search terms.</p>
                            ${searchTerm ? `
                                <button onclick="clearSearchAndFilters()" class="mt-3 text-blue-600 hover:text-blue-800 text-sm">
                                    Clear search and show all responses
                                </button>
                            ` : ''}
                        </div>
                    `;
                } else {
                    // Clear existing content
                    responsesList.innerHTML = '';

                    // Create and append each card properly to avoid HTML rendering issues
                    pageResponses.forEach(response => {
                        const cardElement = document.createElement('div');
                        cardElement.innerHTML = renderResponseCard(response);

                        // Apply search highlighting if needed
                        if (searchTerm) {
                            const searchTerms = searchTerm.toLowerCase().split(' ').filter(term => term.length > 0);
                            searchTerms.forEach(term => {
                                highlightTextInElement(cardElement, term);
                            });
                        }

                        responsesList.appendChild(cardElement.firstElementChild);
                    });
                }

                // Add loaded class to show content smoothly
                responsesList.classList.add('loaded');

                // Animate results count update
                const resultsCount = document.getElementById('results-count');
                resultsCount.classList.add('results-updated');
                setTimeout(() => {
                    resultsCount.classList.remove('results-updated');
                }, 1000);

                // Re-initialize Lucide icons
                lucide.createIcons();
            }, 50); // Small delay to prevent flashing
        }

        function renderResponseCard(response) {
            const priorityClass = calculatePriority(response);
            const sentimentColor = response.original_post?.sentiment === 'positive' ? 'bg-green-100 text-green-800' :
                                  response.original_post?.sentiment === 'negative' ? 'bg-red-100 text-red-800' :
                                  'bg-blue-100 text-blue-800';
            const statusColor = response.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                               response.status === 'approved' ? 'bg-green-100 text-green-800' :
                               'bg-red-100 text-red-800';

            const cardClass = currentView === 'grid' ? 'response-card-grid' : 'response-card-list';

            return `
                <div class="response-card-modern ${cardClass} bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 cursor-pointer" onclick="openResponseModal(${response.id})">
                    <div class="p-6">
                        <!-- Header with status and sentiment -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${sentimentColor}">
                                    ${response.original_post?.sentiment || 'neutral'}
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColor}">
                                    ${response.status}
                                </span>
                                <span class="text-xs text-gray-500">${Math.round((response.confidence_score || 0) * 100)}% confidence</span>
                            </div>
                            <div class="text-xs text-gray-400">
                                ${new Date(response.created_at).toLocaleDateString()}
                            </div>
                        </div>

                        <!-- Author info -->
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <i data-lucide="user" class="w-4 h-4 text-blue-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">@${response.original_post?.author_username || 'unknown'}</div>
                                    <div class="text-xs text-gray-500">${response.original_post?.platform || 'twitter'} • ID: ${response.original_post?.external_id || 'N/A'}</div>
                                </div>
                            </div>
                            ${response.original_post?.post_url ? `
                                <a href="${response.original_post.post_url}" target="_blank" onclick="event.stopPropagation()" class="text-xs text-blue-600 hover:text-blue-800 flex items-center">
                                    <i data-lucide="external-link" class="w-3 h-3 mr-1"></i>
                                    Tweet
                                </a>
                            ` : ''}
                        </div>

                        <!-- Original post preview -->
                        <div class="mb-4">
                            <div class="text-sm text-gray-600 line-clamp-2">
                                ${(response.original_post?.content || 'No content available').substring(0, 120)}${(response.original_post?.content || '').length > 120 ? '...' : ''}
                            </div>
                            ${response.original_post?.media_urls && response.original_post.media_urls.length > 0 ? `
                                <div class="mt-2 flex items-center text-xs text-blue-600">
                                    <i data-lucide="image" class="w-3 h-3 mr-1"></i>
                                    ${response.original_post.media_urls.length} image(s)
                                </div>
                            ` : ''}
                        </div>

                        <!-- AI Response preview -->
                        <div class="bg-blue-50 rounded-lg p-3 mb-4">
                            <div class="text-sm text-blue-900 line-clamp-2">
                                ${(response.response_text || 'No response text').substring(0, 100)}${(response.response_text || '').length > 100 ? '...' : ''}
                            </div>
                        </div>

                        <!-- Quick actions -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-1 flex-wrap">
                                ${response.status === 'pending' ? `
                                    <button onclick="event.stopPropagation(); approveResponse(${response.id})" class="inline-flex items-center px-2 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200 transition-colors mb-1">
                                        <i data-lucide="check" class="w-3 h-3 mr-1"></i>
                                        Approve
                                    </button>
                                    <button onclick="event.stopPropagation(); rejectResponse(${response.id})" class="inline-flex items-center px-2 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200 transition-colors mb-1">
                                        <i data-lucide="x" class="w-3 h-3 mr-1"></i>
                                        Reject
                                    </button>
                                    <button onclick="event.stopPropagation(); regenerateResponse(${response.id})" class="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs hover:bg-purple-200 transition-colors mb-1">
                                        <i data-lucide="refresh-ccw" class="w-3 h-3 mr-1"></i>
                                        Regenerate
                                    </button>
                                ` : `
                                    <span class="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                                        ${response.status === 'approved' ? '✅ Approved' : '❌ Rejected'}
                                    </span>
                                `}
                            </div>
                            <button onclick="event.stopPropagation(); openResponseModal(${response.id})" class="inline-flex items-center px-2 py-1 text-blue-600 hover:text-blue-800 text-xs">
                                <i data-lucide="external-link" class="w-3 h-3 mr-1"></i>
                                Details
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function renderResponseCardWithHighlight(response, searchTerm) {
            // Create the card element directly instead of using innerHTML to avoid HTML rendering issues
            const cardElement = document.createElement('div');
            cardElement.innerHTML = renderResponseCard(response);

            if (searchTerm) {
                // Apply highlighting to text content only, not HTML
                const searchTerms = searchTerm.toLowerCase().split(' ').filter(term => term.length > 0);

                searchTerms.forEach(term => {
                    highlightTextInElement(cardElement, term);
                });
            }

            return cardElement.outerHTML;
        }

        function highlightTextInElement(element, searchTerm) {
            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }

            textNodes.forEach(textNode => {
                const text = textNode.textContent;
                const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');
                if (regex.test(text)) {
                    const highlightedText = text.replace(regex, '<mark class="search-highlight">$1</mark>');
                    const span = document.createElement('span');
                    span.innerHTML = highlightedText;
                    textNode.parentNode.replaceChild(span, textNode);
                }
            });
        }

        function escapeRegExp(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        // Modal Functions
        function openResponseModal(responseId) {
            const response = allResponses.find(r => r.id === responseId);
            if (!response) {
                console.error('Response not found:', responseId);
                return;
            }

            const modal = document.getElementById('response-modal');
            const modalBody = document.getElementById('modal-content-body');

            // Create detailed modal content
            modalBody.innerHTML = `
                <div class="space-y-6">
                    <!-- Response Status and Actions -->
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${response.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : response.status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                ${response.status}
                            </span>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${response.original_post?.sentiment === 'positive' ? 'bg-green-100 text-green-800' : response.original_post?.sentiment === 'negative' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}">
                                ${response.original_post?.sentiment || 'neutral'}
                            </span>
                            <span class="text-sm text-gray-600">${Math.round((response.confidence_score || 0) * 100)}% confidence</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            ${response.status === 'pending' ? `
                                <button onclick="approveResponse(${response.id}); closeResponseModal();" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center">
                                    <i data-lucide="check" class="w-4 h-4 mr-2"></i>
                                    Approve
                                </button>
                                <button onclick="rejectResponse(${response.id}); closeResponseModal();" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center">
                                    <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                                    Reject
                                </button>
                                <button onclick="regenerateResponseInModal(${response.id})" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center">
                                    <i data-lucide="refresh-ccw" class="w-4 h-4 mr-2"></i>
                                    Regenerate
                                </button>
                                <button onclick="editResponseModal(${response.id})" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                                    <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
                                    Edit
                                </button>
                            ` : `
                                <span class="text-sm text-gray-500">
                                    ${response.status === 'approved' ? '✅ Approved' : '❌ Rejected'}
                                </span>
                                ${response.status === 'approved' && response.platform_response_id ? `
                                    <a href="https://twitter.com/user/status/${response.platform_response_id}" target="_blank" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                                        <i data-lucide="external-link" class="w-4 h-4 mr-2"></i>
                                        View Tweet
                                    </a>
                                ` : ''}
                            `}
                        </div>
                    </div>

                    <!-- Author Information -->
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-3">Author Information</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="text-sm font-medium text-gray-500">Username</label>
                                <p class="text-gray-900">@${response.original_post?.author_username || 'unknown'}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-gray-500">Platform</label>
                                <p class="text-gray-900">${response.original_post?.platform || 'twitter'}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-gray-500">Post Date</label>
                                <p class="text-gray-900">${new Date(response.created_at).toLocaleString()}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-gray-500">Response ID</label>
                                <p class="text-gray-900">#${response.id}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-gray-500">Twitter ID</label>
                                <p class="text-gray-900">${response.original_post?.external_id || 'N/A'}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-gray-500">Original Tweet</label>
                                ${response.original_post?.post_url ? `
                                    <a href="${response.original_post.post_url}" target="_blank" class="text-blue-600 hover:text-blue-800 flex items-center">
                                        <i data-lucide="external-link" class="w-3 h-3 mr-1"></i>
                                        View Tweet
                                    </a>
                                ` : '<p class="text-gray-500">N/A</p>'}
                            </div>
                        </div>
                    </div>

                    <!-- Original Post -->
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-3">Original Post</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-gray-800 whitespace-pre-wrap">${response.original_post?.content || 'No content available'}</p>
                            ${response.original_post?.media_urls && response.original_post.media_urls.length > 0 ? `
                                <div class="mt-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Attached Images (${response.original_post.media_urls.length})</h4>
                                    <div class="grid grid-cols-2 gap-2">
                                        ${response.original_post.media_urls.map((url, index) => `
                                            <div class="relative">
                                                <img src="${url}" alt="Attached image ${index + 1}"
                                                     class="w-full h-32 object-cover rounded-lg border border-gray-200 cursor-pointer hover:opacity-80 transition-opacity"
                                                     onclick="openImagePreview('${url}')"
                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                <div class="hidden w-full h-32 bg-gray-200 rounded-lg border border-gray-300 flex items-center justify-center">
                                                    <div class="text-center text-gray-500">
                                                        <i data-lucide="image-off" class="w-6 h-6 mx-auto mb-1"></i>
                                                        <p class="text-xs">Image unavailable</p>
                                                    </div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <!-- AI Generated Response -->
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-3">AI Generated Response</h3>
                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <p class="text-blue-900 whitespace-pre-wrap" id="modal-response-text">${response.response_text || 'No response text'}</p>
                        </div>
                    </div>

                    <!-- Metadata -->
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-3">Metadata</h3>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <label class="font-medium text-gray-500">Priority Level</label>
                                <p class="text-gray-900">${calculatePriority(response)}</p>
                            </div>
                            <div>
                                <label class="font-medium text-gray-500">Processing Time</label>
                                <p class="text-gray-900">${response.processing_time || 'N/A'}</p>
                            </div>
                            <div>
                                <label class="font-medium text-gray-500">Model Version</label>
                                <p class="text-gray-900">${response.model_version || 'Phi-3-mini'}</p>
                            </div>
                            <div>
                                <label class="font-medium text-gray-500">Last Updated</label>
                                <p class="text-gray-900">${new Date(response.updated_at || response.created_at).toLocaleString()}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Show modal
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';

            // Re-initialize Lucide icons
            lucide.createIcons();
        }

        function closeResponseModal() {
            const modal = document.getElementById('response-modal');
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        function editResponseModal(responseId) {
            const response = allResponses.find(r => r.id === responseId);
            if (!response) return;

            const responseTextElement = document.getElementById('modal-response-text');
            const currentText = response.response_text || '';

            // Replace with textarea for editing
            responseTextElement.innerHTML = `
                <textarea id="edit-response-textarea" class="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Edit the AI response...">${currentText}</textarea>
                <div class="flex justify-end space-x-2 mt-3">
                    <button onclick="cancelEditResponse()" class="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200">
                        Cancel
                    </button>
                    <button onclick="saveEditedResponse(${responseId})" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        Save Changes
                    </button>
                </div>
            `;
        }

        function cancelEditResponse() {
            // Reload the modal to cancel editing
            const modal = document.getElementById('response-modal');
            const responseId = parseInt(modal.dataset.responseId);
            if (responseId) {
                openResponseModal(responseId);
            }
        }

        function saveEditedResponse(responseId) {
            const textarea = document.getElementById('edit-response-textarea');
            const newText = textarea.value.trim();

            if (!newText) {
                alert('Response text cannot be empty');
                return;
            }

            // Update the response
            editResponse(responseId, newText);
            closeResponseModal();
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('response-modal');
            if (event.target === modal) {
                closeResponseModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeResponseModal();
                closeImagePreview();
            }
        });

        // Image Preview Functions
        let currentImageUrl = '';

        function openImageModal(imageUrl) {
            openImagePreview(imageUrl);
        }

        function openImagePreview(imageUrl) {
            currentImageUrl = imageUrl;
            const modal = document.getElementById('image-preview-modal');
            const previewImage = document.getElementById('preview-image');

            previewImage.src = imageUrl;
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';

            // Hide analysis results
            document.getElementById('image-analysis-results').classList.add('hidden');
        }

        function closeImagePreview() {
            const modal = document.getElementById('image-preview-modal');
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
            currentImageUrl = '';
        }

        async function analyzeImage() {
            if (!currentImageUrl) return;

            try {
                showNotification('Analyzing image with AI...', 'info');

                // This would call your multimodal API endpoint
                const response = await fetch(`${API_BASE}/api/v1/multimodal/analyze-image`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        image_url: currentImageUrl
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    displayImageAnalysis(data.analysis);
                    showNotification('Image analysis completed', 'success');
                } else {
                    showNotification('Failed to analyze image', 'error');
                }
            } catch (error) {
                console.error('Error analyzing image:', error);
                showNotification('Error analyzing image', 'error');
            }
        }

        function displayImageAnalysis(analysis) {
            const resultsDiv = document.getElementById('image-analysis-results');
            const contentDiv = document.getElementById('analysis-content');

            contentDiv.innerHTML = `
                <div class="space-y-3">
                    <div>
                        <span class="font-medium text-blue-800">Description:</span>
                        <p class="text-blue-700 mt-1">${analysis.description || 'No description available'}</p>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <span class="font-medium text-blue-800">Airline Related:</span>
                            <span class="ml-2 px-2 py-1 rounded text-xs ${analysis.airline_related ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                ${analysis.airline_related ? 'Yes' : 'No'}
                            </span>
                        </div>
                        <div>
                            <span class="font-medium text-blue-800">Sentiment:</span>
                            <span class="ml-2 px-2 py-1 rounded text-xs ${analysis.sentiment === 'positive' ? 'bg-green-100 text-green-800' : analysis.sentiment === 'negative' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}">
                                ${analysis.sentiment || 'neutral'}
                            </span>
                        </div>
                        <div>
                            <span class="font-medium text-blue-800">Service Issue:</span>
                            <span class="ml-2 px-2 py-1 rounded text-xs ${analysis.service_issue ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}">
                                ${analysis.service_issue ? 'Detected' : 'None'}
                            </span>
                        </div>
                        <div>
                            <span class="font-medium text-blue-800">Urgency:</span>
                            <span class="ml-2 px-2 py-1 rounded text-xs ${analysis.urgency_level === 'high' ? 'bg-red-100 text-red-800' : analysis.urgency_level === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}">
                                ${analysis.urgency_level || 'low'}
                            </span>
                        </div>
                    </div>
                    ${analysis.visible_text ? `
                        <div>
                            <span class="font-medium text-blue-800">Text in Image:</span>
                            <p class="text-blue-700 mt-1 bg-white p-2 rounded border">${analysis.visible_text}</p>
                        </div>
                    ` : ''}
                </div>
            `;

            resultsDiv.classList.remove('hidden');
        }

        function downloadImage() {
            if (!currentImageUrl) return;

            const link = document.createElement('a');
            link.href = currentImageUrl;
            link.download = `image_${Date.now()}.jpg`;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // View Toggle Functions
        function toggleView(viewType) {
            currentView = viewType;
            const responsesList = document.getElementById('responses-list');
            const listBtn = document.getElementById('list-view-btn');
            const gridBtn = document.getElementById('grid-view-btn');

            // Update button states
            if (viewType === 'list') {
                listBtn.className = 'px-3 py-1 rounded text-sm font-medium transition-colors bg-white text-gray-900 shadow-sm';
                gridBtn.className = 'px-3 py-1 rounded text-sm font-medium transition-colors text-gray-600 hover:text-gray-900';
                responsesList.className = 'responses-list';
            } else {
                gridBtn.className = 'px-3 py-1 rounded text-sm font-medium transition-colors bg-white text-gray-900 shadow-sm';
                listBtn.className = 'px-3 py-1 rounded text-sm font-medium transition-colors text-gray-600 hover:text-gray-900';
                responsesList.className = 'responses-grid';
            }

            // Re-render responses with new view
            displayResponses();
        }

        // Regenerate Functions
        async function regenerateAllResponses() {
            if (!confirm('Are you sure you want to regenerate all pending responses? This will replace existing AI responses.')) {
                return;
            }

            const pendingResponses = allResponses.filter(r => r.status === 'pending');
            if (pendingResponses.length === 0) {
                showNotification('No pending responses to regenerate', 'info');
                return;
            }

            // Find and disable the "Regenerate All" button
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i data-lucide="loader" class="w-4 h-4 mr-2 animate-spin"></i>Regenerating All...';
            button.disabled = true;
            button.classList.add('opacity-50', 'cursor-not-allowed');

            showNotification(`🔄 Regenerating ${pendingResponses.length} responses...`, 'info');

            try {
                let successCount = 0;
                let errorCount = 0;

                for (const response of pendingResponses) {
                    try {
                        await regenerateResponse(response.id, null, false); // Don't show individual confirmations
                        successCount++;
                        showNotification(`✅ Regenerated ${successCount}/${pendingResponses.length} responses`, 'info');
                    } catch (error) {
                        errorCount++;
                        console.error(`Failed to regenerate response ${response.id}:`, error);
                    }
                    // Small delay to avoid overwhelming the API
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                if (errorCount === 0) {
                    showNotification(`🎉 Successfully regenerated all ${successCount} responses!`, 'success');
                } else {
                    showNotification(`⚠️ Regenerated ${successCount} responses, ${errorCount} failed`, 'warning');
                }

                await loadRealResponses(); // Refresh the list
            } catch (error) {
                console.error('Error regenerating responses:', error);
                showNotification('❌ Failed to regenerate responses', 'error');
            } finally {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        }

        async function regenerateResponse(responseId, originalPostId, showConfirm = true) {
            if (showConfirm && !confirm('Are you sure you want to regenerate this response? The current response will be replaced.')) {
                return;
            }

            // Find the button that triggered this (if called from button click)
            let button = null;
            if (event && event.target) {
                button = event.target.closest('button');
            }

            // Store original button state
            let originalText = '';
            if (button) {
                originalText = button.innerHTML;
                button.innerHTML = '<i data-lucide="loader" class="w-3 h-3 mr-1 animate-spin"></i>Regenerating...';
                button.disabled = true;
                button.classList.add('opacity-50', 'cursor-not-allowed');
            }

            try {
                showNotification('🔄 Regenerating response...', 'info');

                const response = await fetch(`${API_BASE}/api/v1/responses/${responseId}/regenerate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    showNotification('✅ Response regenerated successfully!', 'success');

                    // Refresh the responses to show the new content
                    await loadRealResponses();

                    return data;
                } else {
                    throw new Error(data.error || `Failed to regenerate response: ${response.status}`);
                }
            } catch (error) {
                console.error('Error regenerating response:', error);
                showNotification(`❌ Error regenerating response: ${error.message}`, 'error');
                throw error;
            } finally {
                // Restore button state
                if (button) {
                    button.innerHTML = originalText;
                    button.disabled = false;
                    button.classList.remove('opacity-50', 'cursor-not-allowed');
                }
            }
        }

        async function regenerateResponseInModal(responseId) {
            if (!confirm('Are you sure you want to regenerate this response? The current response will be replaced.')) {
                return;
            }

            // Find the regenerate button in the modal
            const button = event.target.closest('button');
            const originalText = button.innerHTML;

            try {
                // Update button state
                button.innerHTML = '<i data-lucide="loader" class="w-4 h-4 mr-2 animate-spin"></i>Regenerating...';
                button.disabled = true;
                button.classList.add('opacity-50', 'cursor-not-allowed');

                showNotification('🔄 Regenerating response...', 'info');

                const response = await fetch(`${API_BASE}/api/v1/responses/${responseId}/regenerate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    showNotification('✅ Response regenerated successfully!', 'success');

                    // Refresh the responses data
                    await loadRealResponses();

                    // Find the updated response and refresh the modal content
                    const updatedResponse = allResponses.find(r => r.id === responseId);
                    if (updatedResponse) {
                        // Update the modal content with the new response
                        showResponseModal(updatedResponse);
                    } else {
                        // If we can't find the updated response, close the modal
                        closeResponseModal();
                    }

                } else {
                    throw new Error(data.error || `Failed to regenerate response: ${response.status}`);
                }
            } catch (error) {
                console.error('Error regenerating response:', error);
                showNotification(`❌ Error regenerating response: ${error.message}`, 'error');
            } finally {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        }

        function clearSearchAndFilters() {
            document.getElementById('search-input').value = '';
            clearAllFilters();
        }

        function updatePagination() {
            // Pagination removed in favor of infinite scroll
            // This function is kept for compatibility but does nothing
            return;
        }

        function changePage(direction) {
            const totalPages = Math.ceil(filteredResponses.length / responsesPerPage);
            const newPage = currentPage + direction;

            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                displayResponses();
            }
        }

        function exportResponses() {
            const dataToExport = filteredResponses.map(response => ({
                id: response.id,
                status: response.status,
                created_at: response.created_at,
                original_post_content: response.original_post?.content || '',
                original_post_author: response.original_post?.author_username || '',
                sentiment: response.original_post?.sentiment || '',
                response_text: response.response_text || '',
                confidence: response.confidence || '',
                platform: response.original_post?.platform || ''
            }));

            const csv = convertToCSV(dataToExport);
            downloadCSV(csv, `responses_export_${new Date().toISOString().split('T')[0]}.csv`);

            showNotification(`Exported ${dataToExport.length} responses`, 'success');
        }

        function convertToCSV(data) {
            if (data.length === 0) return '';

            const headers = Object.keys(data[0]);
            const csvContent = [
                headers.join(','),
                ...data.map(row =>
                    headers.map(header => {
                        const value = row[header] || '';
                        return `"${value.toString().replace(/"/g, '""')}"`;
                    }).join(',')
                )
            ].join('\n');

            return csvContent;
        }

        function downloadCSV(csv, filename) {
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Enhanced loadRealResponses function to populate allResponses
        async function loadRealResponses() {
            try {
                console.log('Loading responses...');

                // Show loading state
                const responsesList = document.getElementById('responses-list');
                if (responsesList) {
                    responsesList.classList.remove('loaded');
                    responsesList.innerHTML = `
                        <div class="responses-loading">
                            <div class="spinner">
                                <i data-lucide="loader-2" class="w-6 h-6"></i>
                            </div>
                            <span>Loading responses...</span>
                        </div>
                    `;
                    lucide.createIcons();
                }

                const response = await fetch(`${API_BASE}/api/v1/responses/`);

                if (response.ok) {
                    const data = await response.json();
                    console.log('Raw response data:', data);

                    // Handle different response formats
                    let responses = [];
                    if (Array.isArray(data)) {
                        responses = data;
                    } else if (data.responses && Array.isArray(data.responses)) {
                        responses = data.responses;
                    } else {
                        console.warn('Unexpected response format:', data);
                        responses = [];
                    }

                    // Update global variables
                    allResponses = responses;
                    filteredResponses = [...allResponses];
                    isDataLoaded = true;
                    currentPage = 1;

                    console.log(`Loaded ${allResponses.length} responses`);

                    // Use displayResponses function for consistent rendering
                    displayResponses();

                    // Initialize filters
                    applyFilters();

                } else {
                    console.error('Failed to load responses:', response.status);
                    allResponses = [];
                    filteredResponses = [];
                    isDataLoaded = false;
                }
            } catch (error) {
                console.error('Error loading responses:', error);
                allResponses = [];
                filteredResponses = [];
                isDataLoaded = false;
            }
        }

        // Initialize filtering system
        function initializeFiltering() {
            // Ensure all filter elements exist
            const requiredElements = [
                'search-input', 'status-filter', 'sentiment-filter', 'platform-filter',
                'priority-filter', 'date-filter', 'response-time-filter', 'confidence-filter',
                'sort-filter', 'responses-list'
            ];

            let allElementsExist = true;
            requiredElements.forEach(id => {
                if (!document.getElementById(id)) {
                    console.warn(`Required element not found: ${id}`);
                    allElementsExist = false;
                }
            });

            if (allElementsExist) {
                console.log('All filter elements found, initializing...');
                // Initialize with empty arrays to prevent errors
                allResponses = [];
                filteredResponses = [];
                isDataLoaded = false;

                // Load data
                loadRealResponses();
            } else {
                console.log('Some filter elements missing, retrying in 1 second...');
                setTimeout(initializeFiltering, 1000);
            }
        }

        // Sticky search bar and scroll enhancements
        function setupStickySearchBar() {
            const searchBar = document.getElementById('sticky-search-bar');
            const scrollToTopBtn = document.getElementById('scroll-to-top');
            let lastScrollY = window.scrollY;

            window.addEventListener('scroll', () => {
                const currentScrollY = window.scrollY;

                // Add scrolled class to search bar when scrolling
                if (currentScrollY > 50) {
                    searchBar?.classList.add('scrolled');
                } else {
                    searchBar?.classList.remove('scrolled');
                }

                // Show/hide scroll to top button
                if (currentScrollY > 300) {
                    scrollToTopBtn?.classList.add('visible');
                } else {
                    scrollToTopBtn?.classList.remove('visible');
                }

                lastScrollY = currentScrollY;
            });
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Enhanced infinite scroll with smooth loading (integrated into main setupInfiniteScroll)

        // System Status Management
        let autoRefreshInterval = null;
        let systemStatusData = {
            backend: { status: 'unknown', lastCheck: null, responseTime: null },
            adminui: { status: 'running', lastCheck: null, responseTime: null },
            database: { status: 'unknown', lastCheck: null },
            ai: { status: 'unknown', lastCheck: null },
            performance: { status: 'unknown', lastCheck: null }
        };

        async function refreshSystemStatus() {
            console.log('🔍 Refreshing system status...');

            // Update last check time
            const now = new Date();
            const lastCheckEl = document.getElementById('last-check-time');
            if (lastCheckEl) {
                lastCheckEl.textContent = now.toLocaleTimeString();
            }

            // Check all services
            await Promise.all([
                checkBackendStatus(),
                checkAdminUIStatus(),
                checkDatabaseStatus(),
                checkAIModelStatus(),
                checkPerformanceStatus()
            ]);

            // Update overall status
            updateOverallStatus();
        }

        async function checkBackendStatus() {
            const startTime = Date.now();
            const indicator = document.getElementById('backend-status-indicator');
            const badge = document.getElementById('backend-status-badge');
            const responseTimeEl = document.getElementById('backend-response-time');
            const lastCheckEl = document.getElementById('backend-last-check');
            const modelEl = document.getElementById('backend-model');
            const startBtn = document.getElementById('backend-start-btn');
            const stopBtn = document.getElementById('backend-stop-btn');

            if (!indicator) return; // Not on system status page

            try {
                const response = await fetch(`${API_BASE}/health`, { timeout: 5000 });
                const responseTime = Date.now() - startTime;

                if (response.ok) {
                    const data = await response.json();

                    // Update UI
                    indicator.className = 'w-3 h-3 rounded-full bg-green-400 mr-3';
                    badge.className = 'px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-600';
                    badge.textContent = 'Running';
                    responseTimeEl.textContent = `${responseTime}ms`;
                    lastCheckEl.textContent = new Date().toLocaleTimeString();
                    modelEl.textContent = data.model || 'Phi-3 Mini';

                    // Show stop button, hide start button
                    if (startBtn) startBtn.classList.add('hidden');
                    if (stopBtn) stopBtn.classList.remove('hidden');

                    systemStatusData.backend = { status: 'running', lastCheck: new Date(), responseTime };

                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('Backend check failed:', error);

                // Update UI for failed state
                indicator.className = 'w-3 h-3 rounded-full bg-red-400 mr-3';
                badge.className = 'px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-600';
                badge.textContent = 'Down';
                responseTimeEl.textContent = 'Timeout';
                lastCheckEl.textContent = new Date().toLocaleTimeString();
                modelEl.textContent = 'Unavailable';

                // Show start button, hide stop button
                if (startBtn) startBtn.classList.remove('hidden');
                if (stopBtn) stopBtn.classList.add('hidden');

                systemStatusData.backend = { status: 'down', lastCheck: new Date(), responseTime: null };
            }
        }

        async function checkAdminUIStatus() {
            const indicator = document.getElementById('adminui-status-indicator');
            const badge = document.getElementById('adminui-status-badge');
            const responseTimeEl = document.getElementById('adminui-response-time');
            const lastCheckEl = document.getElementById('adminui-last-check');

            if (!indicator) return; // Not on system status page

            // Admin UI is always running since we're using it
            indicator.className = 'w-3 h-3 rounded-full bg-green-400 mr-3';
            badge.className = 'px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-600';
            badge.textContent = 'Running';
            responseTimeEl.textContent = '<10ms';
            lastCheckEl.textContent = new Date().toLocaleTimeString();

            systemStatusData.adminui = { status: 'running', lastCheck: new Date(), responseTime: 5 };
        }

        async function checkDatabaseStatus() {
            const indicator = document.getElementById('database-status-indicator');
            const responsesEl = document.getElementById('db-responses-count');
            const postsEl = document.getElementById('db-posts-count');
            const accountsEl = document.getElementById('db-accounts-count');
            const pendingEl = document.getElementById('db-pending-count');

            if (!indicator) return; // Not on system status page

            try {
                const response = await fetch(`${API_BASE}/api/v1/responses/`);
                if (response.ok) {
                    const data = await response.json();

                    indicator.className = 'w-3 h-3 rounded-full bg-green-400 mr-3';
                    responsesEl.textContent = data.responses?.length || 0;
                    pendingEl.textContent = data.responses?.filter(r => r.status === 'pending').length || 0;

                    // Get accounts count
                    try {
                        const accountsResponse = await fetch(`${API_BASE}/api/v1/social-accounts/`);
                        if (accountsResponse.ok) {
                            const accountsData = await accountsResponse.json();
                            accountsEl.textContent = accountsData.accounts?.length || 0;
                        }
                    } catch (e) {
                        accountsEl.textContent = '0';
                    }

                    postsEl.textContent = data.responses?.length || 0; // Approximate

                    systemStatusData.database = { status: 'running', lastCheck: new Date() };
                } else {
                    throw new Error('Database check failed');
                }
            } catch (error) {
                indicator.className = 'w-3 h-3 rounded-full bg-red-400 mr-3';
                responsesEl.textContent = 'Error';
                postsEl.textContent = 'Error';
                accountsEl.textContent = 'Error';
                pendingEl.textContent = 'Error';

                systemStatusData.database = { status: 'down', lastCheck: new Date() };
            }
        }

        async function checkAIModelStatus() {
            const indicator = document.getElementById('ai-status-indicator');
            const statusEl = document.getElementById('ai-model-status');
            const lastResponseEl = document.getElementById('ai-last-response');

            if (!indicator) return; // Not on system status page

            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    const data = await response.json();

                    indicator.className = 'w-3 h-3 rounded-full bg-green-400 mr-3';
                    statusEl.textContent = 'Ready';
                    lastResponseEl.textContent = 'Active';

                    systemStatusData.ai = { status: 'running', lastCheck: new Date() };
                } else {
                    throw new Error('AI model check failed');
                }
            } catch (error) {
                indicator.className = 'w-3 h-3 rounded-full bg-red-400 mr-3';
                statusEl.textContent = 'Unavailable';
                lastResponseEl.textContent = 'Error';

                systemStatusData.ai = { status: 'down', lastCheck: new Date() };
            }
        }

        async function checkPerformanceStatus() {
            const indicator = document.getElementById('performance-status-indicator');
            const latencyEl = document.getElementById('perf-api-latency');
            const memoryEl = document.getElementById('perf-memory');
            const uptimeEl = document.getElementById('perf-uptime');

            if (!indicator) return; // Not on system status page

            // Mock performance data (in real implementation, this would come from backend)
            indicator.className = 'w-3 h-3 rounded-full bg-green-400 mr-3';
            latencyEl.textContent = `${Math.floor(Math.random() * 50) + 10}ms`;
            memoryEl.textContent = `${(Math.random() * 2 + 1).toFixed(1)}GB`;

            // Calculate uptime (mock)
            const uptimeHours = Math.floor(Math.random() * 24) + 1;
            uptimeEl.textContent = `${uptimeHours}h ${Math.floor(Math.random() * 60)}m`;

            systemStatusData.performance = { status: 'good', lastCheck: new Date() };
        }

        function updateOverallStatus() {
            const indicator = document.getElementById('overall-status-indicator');
            const textEl = document.getElementById('overall-status-text');
            const detailsEl = document.getElementById('overall-status-details');

            const statuses = Object.values(systemStatusData).map(s => s.status);
            const downCount = statuses.filter(s => s === 'down').length;
            const runningCount = statuses.filter(s => s === 'running' || s === 'good').length;

            let statusColor, statusText, statusDetails;

            if (downCount === 0) {
                statusColor = 'green';
                statusText = 'All Systems Operational';
                statusDetails = `${runningCount} services running normally`;
            } else if (downCount < statuses.length / 2) {
                statusColor = 'yellow';
                statusText = 'Partial Outage';
                statusDetails = `${downCount} service(s) down, ${runningCount} running`;
            } else {
                statusColor = 'red';
                statusText = 'System Down';
                statusDetails = `${downCount} service(s) down`;
            }

            // Update main status page if visible
            if (indicator) {
                indicator.className = `w-4 h-4 rounded-full bg-${statusColor}-400`;
                textEl.className = `text-2xl font-bold text-${statusColor}-600`;
                textEl.textContent = statusText;
                detailsEl.textContent = statusDetails;
            }

            // Update footer status indicator
            updateFooterStatus(statusColor, statusText);
        }

        function updateFooterStatus(statusColor, statusText) {
            const footerIndicator = document.getElementById('footer-status-indicator');
            const footerText = document.getElementById('footer-status-text');

            if (footerIndicator && footerText) {
                footerIndicator.className = `w-2 h-2 bg-${statusColor}-400 rounded-full`;
                footerText.textContent = statusText;
            }
        }

        function toggleAutoRefresh() {
            const toggle = document.getElementById('auto-refresh-toggle');
            const statusEl = document.getElementById('auto-refresh-status');

            if (!toggle || !statusEl) return;

            if (toggle.checked) {
                statusEl.textContent = 'Enabled';
                statusEl.className = 'text-2xl font-bold text-green-600';

                // Start auto refresh every 30 seconds
                autoRefreshInterval = setInterval(refreshSystemStatus, 30000);
                console.log('✅ Auto refresh enabled');
            } else {
                statusEl.textContent = 'Disabled';
                statusEl.className = 'text-2xl font-bold text-gray-500';

                // Stop auto refresh
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
                console.log('🛑 Auto refresh disabled');
            }
        }

        // Debug Mode Management
        let debugMode = false;
        let debugLogInterval = null;

        function toggleDebugMode() {
            const toggle = document.getElementById('debug-mode-toggle');
            const statusEl = document.getElementById('debug-mode-status');

            if (!toggle || !statusEl) return;

            debugMode = toggle.checked;

            if (debugMode) {
                statusEl.textContent = 'Enabled';
                statusEl.className = 'text-2xl font-bold text-green-600';

                // Start debug log polling every 5 seconds
                debugLogInterval = setInterval(fetchDebugLogs, 5000);
                console.log('🐛 Debug mode enabled - detailed logs will be shown');
                showNotification('Debug mode enabled - detailed logs will be shown', 'info');

                // Immediately fetch logs
                fetchDebugLogs();
            } else {
                statusEl.textContent = 'Disabled';
                statusEl.className = 'text-2xl font-bold text-gray-500';

                // Stop debug log polling
                if (debugLogInterval) {
                    clearInterval(debugLogInterval);
                    debugLogInterval = null;
                }
                console.log('🛑 Debug mode disabled');
                showNotification('Debug mode disabled', 'info');
            }
        }

        async function fetchDebugLogs() {
            if (!debugMode) return;

            try {
                // Fetch recent logs from backend
                const response = await fetch(`${API_BASE}/api/v1/debug/logs`, {
                    headers: {
                        'Authorization': `Bearer ${sessionToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateDebugLogsDisplay(data.logs);
                }
            } catch (error) {
                console.error('Error fetching debug logs:', error);
            }
        }

        function updateDebugLogsDisplay(logs) {
            const logsContainer = document.getElementById('logs-container');
            if (!logsContainer || !debugMode) return;

            // Only update if we're viewing backend logs
            const logSource = document.getElementById('log-source');
            if (logSource && logSource.value === 'backend') {
                const logLines = logs.map(log => {
                    const timestamp = new Date(log.timestamp).toLocaleTimeString();
                    const level = log.level.toUpperCase();
                    const levelColor = {
                        'INFO': 'text-green-400',
                        'WARNING': 'text-yellow-400',
                        'ERROR': 'text-red-400',
                        'DEBUG': 'text-blue-400'
                    }[level] || 'text-gray-400';

                    return `<div class="mb-1">
                        <span class="text-gray-500">[${timestamp}]</span>
                        <span class="${levelColor}">${level}:</span>
                        <span class="text-green-400">${log.message}</span>
                    </div>`;
                }).join('');

                logsContainer.innerHTML = logLines || '<div class="text-gray-500">No debug logs available</div>';

                // Auto-scroll to bottom
                logsContainer.scrollTop = logsContainer.scrollHeight;
            }
        }

        // User Management System
        let currentUser = null;
        let sessionToken = localStorage.getItem('session_token');

        // Authentication Functions

        async function logout() {
            console.log('🚪 Logout function called');

            try {
                if (sessionToken) {
                    console.log('📤 Sending logout request to server');
                    await fetch(`${API_BASE}/api/v1/auth/logout`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${sessionToken}`
                        }
                    });
                }
            } catch (error) {
                console.error('Logout error:', error);
            }

            console.log('🧹 Clearing session data');
            sessionToken = null;
            currentUser = null;
            localStorage.removeItem('session_token');
            localStorage.removeItem('current_user');

            console.log('🔄 Redirecting to login page');
            // Redirect to login page
            window.location.href = 'login.html';
        }

        async function checkAuthentication() {
            if (!sessionToken) {
                redirectToLogin();
                return false;
            }

            try {
                const response = await fetch(`${API_BASE}/api/v1/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${sessionToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    currentUser = data.user;
                    initializeUserInterface();
                    return true;
                } else {
                    sessionToken = null;
                    localStorage.removeItem('session_token');
                    localStorage.removeItem('current_user');
                    redirectToLogin();
                    return false;
                }
            } catch (error) {
                console.error('Auth check error:', error);
                redirectToLogin();
                return false;
            }
        }

        function redirectToLogin() {
            window.location.href = 'login.html';
        }

        function initializeUserInterface() {
            if (!currentUser) return;

            // Show/hide navigation items based on role
            const userManagementNav = document.querySelector('.nav-item[data-section="user-management"]');

            // Get navigation elements
            const credentialManagementNav = document.querySelector('.nav-item[data-section="credential-management"]');
            const systemStatusNav = document.querySelector('.nav-item[data-section="system-status"]');
            const modelManagementNav = document.querySelector('.nav-item[data-section="model-management"]');

            // Hide admin-only sections by default
            [userManagementNav, credentialManagementNav].forEach(nav => {
                if (nav) nav.style.display = 'none';
            });

            // Show based on role
            if (currentUser.role === 'admin') {
                // Admin sees everything
                [userManagementNav, credentialManagementNav].forEach(nav => {
                    if (nav) nav.style.display = 'flex';
                });
            } else if (currentUser.role === 'infra') {
                // Infra sees system status and model management (these are always visible)
                // No need to show/hide as they're part of the main navigation
            } else if (currentUser.role === 'marketing') {
                // Marketing users can see Response Management and My Profile
                const allowedSections = ['responses', 'my-profile'];

                document.querySelectorAll('.nav-item').forEach(item => {
                    const section = item.dataset.section;
                    if (!allowedSections.includes(section)) {
                        item.style.display = 'none';
                    } else {
                        item.style.display = 'flex';
                    }
                });

                // Hide dashboard for marketing users - redirect to response management
                const dashboardItem = document.querySelector('.nav-item[data-section="dashboard"]');
                if (dashboardItem) {
                    dashboardItem.style.display = 'none';
                }

                // Auto-redirect marketing users to response management
                setTimeout(() => {
                    showSection('responses');
                }, 100);
            }

            // Update user info in header
            updateUserHeader();
        }

        function updateUserHeader() {
            if (!currentUser) return;

            // Update desktop header user info
            updateDesktopUserInfo();

            // Update mobile header user avatar
            updateMobileUserAvatar();

            // Update mobile sidebar user info
            updateMobileSidebarUserInfo();
        }

        function updateDesktopUserInfo() {
            // Add user info to desktop header (hidden on mobile)
            const desktopContainer = document.querySelector('#header-right-section .hidden.lg\\:flex');
            if (desktopContainer && currentUser) {
                // Remove existing user info if any
                const existingUserInfo = document.getElementById('desktop-user-info');
                if (existingUserInfo) {
                    existingUserInfo.remove();
                }

                // Add user info after the system status
                const userInfo = document.createElement('div');
                userInfo.id = 'desktop-user-info';
                userInfo.className = 'flex items-center space-x-3';
                userInfo.innerHTML = `
                    <div class="text-right">
                        <div class="text-sm font-medium text-gray-900">${currentUser.full_name}</div>
                        <div class="text-xs text-gray-500">${currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1)} • @${currentUser.username}</div>
                    </div>
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">${currentUser.full_name.charAt(0)}</span>
                    </div>
                    <button onclick="logout()" class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg transition-colors flex items-center" title="Logout">
                        <i data-lucide="log-out" class="w-4 h-4 mr-1"></i>
                        Logout
                    </button>
                `;

                desktopContainer.appendChild(userInfo);
            }
        }

        function updateMobileUserAvatar() {
            // Add user avatar to mobile header
            const mobileAvatarContainer = document.getElementById('mobile-user-avatar');
            if (mobileAvatarContainer && currentUser) {
                // Remove existing avatar if any
                const existingAvatar = document.getElementById('mobile-avatar');
                if (existingAvatar) {
                    existingAvatar.remove();
                }

                // Add user avatar
                const avatar = document.createElement('div');
                avatar.id = 'mobile-avatar';
                avatar.className = 'w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center cursor-pointer';
                avatar.innerHTML = `
                    <span class="text-white text-sm font-medium">${currentUser.full_name.charAt(0)}</span>
                `;

                // Optional: Add click handler to open sidebar
                avatar.addEventListener('click', () => {
                    const hamburger = document.getElementById('hamburger-menu');
                    if (hamburger) {
                        hamburger.click();
                    }
                });

                mobileAvatarContainer.appendChild(avatar);
            }
        }

        function updateMobileSidebarUserInfo() {
            // Update mobile sidebar user info (only show on mobile)
            const sidebarUserSection = document.getElementById('sidebar-user-section');
            const sidebarUsername = document.getElementById('sidebar-username');
            const sidebarUserRole = document.getElementById('sidebar-user-role');

            if (sidebarUserSection && sidebarUsername && sidebarUserRole && currentUser) {
                // Only show on mobile (screen width < 1024px)
                if (window.innerWidth < 1024) {
                    sidebarUserSection.classList.remove('hidden');

                    // Update user info
                    sidebarUsername.textContent = currentUser.full_name;
                    sidebarUserRole.textContent = `${currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1)} • @${currentUser.username}`;
                } else {
                    // Hide on desktop
                    sidebarUserSection.classList.add('hidden');
                }
            }

            // Re-initialize icons
            lucide.createIcons();
        }

        // User Management Functions
        async function loadUsers() {
            if (!hasPermission('users.view')) return;

            try {
                const response = await fetch(`${API_BASE}/api/v1/users/`, {
                    headers: {
                        'Authorization': `Bearer ${sessionToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayUsers(data.users);
                    updateUserStats(data.users);
                } else {
                    showNotification('Failed to load users', 'error');
                }
            } catch (error) {
                console.error('Error loading users:', error);
                showNotification('Error loading users', 'error');
            }
        }

        function displayUsers(users) {
            const tbody = document.getElementById('users-table-body');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';

                const roleColors = {
                    admin: 'bg-green-100 text-green-800',
                    infra: 'bg-purple-100 text-purple-800',
                    marketing: 'bg-orange-100 text-orange-800'
                };

                const statusColor = user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                const assignedAccounts = user.assigned_accounts || [];

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                                ${user.profile_image ?
                                    `<img src="${user.profile_image}" alt="Profile" class="w-10 h-10 rounded-full object-cover">` :
                                    `<span class="text-gray-600 font-medium">${user.full_name.charAt(0)}</span>`
                                }
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">${user.full_name}</div>
                                <div class="text-sm text-gray-500">@${user.username}</div>
                                <div class="text-xs text-gray-400">${user.email}</div>
                                ${user.job_title ? `<div class="text-xs text-blue-600">${user.job_title}</div>` : ''}
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${roleColors[user.role]}">
                            ${user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${statusColor}">
                            ${user.is_active ? 'Active' : 'Inactive'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${user.created_at ? new Date(user.created_at).toLocaleDateString() : '-'}
                        ${user.created_by ? `<br><span class="text-xs">by ${user.created_by}</span>` : ''}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${user.role === 'marketing' ?
                            `<div class="flex flex-wrap gap-1">
                                ${assignedAccounts.map(acc =>
                                    `<span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">@${acc.username}</span>`
                                ).join('')}
                                ${assignedAccounts.length === 0 ? '<span class="text-gray-400">None assigned</span>' : ''}
                            </div>` :
                            '<span class="text-gray-400">N/A</span>'
                        }
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            <button onclick="showEditUserModal(${user.id})"
                                    class="text-blue-600 hover:text-blue-900" title="Edit User">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            ${user.role === 'marketing' ? `
                                <button onclick="showAssignAccountsModal(${user.id}, '${user.full_name}')"
                                        class="text-green-600 hover:text-green-900" title="Assign Accounts">
                                    <i data-lucide="link" class="w-4 h-4"></i>
                                </button>
                            ` : ''}
                            <button onclick="resetUserPassword(${user.id}, '${user.username}')"
                                    class="text-orange-600 hover:text-orange-900" title="Reset Password">
                                <i data-lucide="key" class="w-4 h-4"></i>
                            </button>
                            <button onclick="deleteUser(${user.id}, '${user.username}')"
                                    class="text-red-600 hover:text-red-900" title="Delete User"
                                    ${user.username === 'admin' ? 'disabled' : ''}>
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });

            // Re-initialize icons
            lucide.createIcons();
        }

        function updateUserStats(users) {
            const totalCount = users.length;
            const adminCount = users.filter(u => u.role === 'admin').length;
            const infraCount = users.filter(u => u.role === 'infra').length;
            const marketingCount = users.filter(u => u.role === 'marketing').length;

            document.getElementById('total-users-count').textContent = totalCount;
            document.getElementById('admin-users-count').textContent = adminCount;
            document.getElementById('infra-users-count').textContent = infraCount;
            document.getElementById('marketing-users-count').textContent = marketingCount;
        }

        // Update showSection to handle sidebar navigation and authentication
        const originalShowSection = showSection;
        showSection = function(sectionId) {
            // Update sidebar navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
                if (item.dataset.section === sectionId) {
                    item.classList.add('active');
                }
            });

            // Check permissions before showing sections
            if (sectionId === 'user-management' && !hasPermission('users.view')) {
                showNotification('Access denied: Insufficient permissions', 'error');
                return;
            }

            if (sectionId === 'credential-management' && !hasPermission('credentials.view')) {
                showNotification('Access denied: Insufficient permissions', 'error');
                return;
            }

            // Marketing users can only access responses and my-profile
            if (currentUser && currentUser.role === 'marketing') {
                const allowedSections = ['responses', 'my-profile'];
                if (!allowedSections.includes(sectionId)) {
                    showNotification('Access denied: Marketing users can only access Response Management and My Profile', 'error');
                    showSection('responses');
                    return;
                }
            }

            // Call original function FIRST
            originalShowSection(sectionId);

            // Load section-specific data
            if (sectionId === 'user-management') {
                loadUsers();
            } else if (sectionId === 'credential-management') {
                loadCredentials();
            }

            // Close mobile sidebar AFTER navigation (with small delay to ensure navigation completes)
            if (window.innerWidth < 1024) {
                setTimeout(() => {
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebar-overlay');

                    if (sidebar) {
                        sidebar.classList.add('sidebar-closed');
                    }

                    if (overlay) {
                        overlay.classList.add('hidden');
                        overlay.style.display = 'none';
                    }

                    document.body.style.overflow = '';
                }, 100);
            }
        };



        // Global close sidebar function
        function closeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            sidebar.classList.add('sidebar-closed');
            overlay.classList.add('hidden');
            overlay.style.display = 'none';
            document.body.style.overflow = '';
        }

        // Sidebar functionality
        function initializeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            const hamburger = document.getElementById('hamburger-menu');

            // Hamburger menu click
            hamburger?.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Hamburger clicked'); // Debug
                sidebar.classList.remove('sidebar-closed');
                overlay.classList.remove('hidden');
                overlay.style.display = 'block';
                document.body.style.overflow = 'hidden';
            });

            overlay?.addEventListener('click', closeSidebar);

            // Handle window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 1024) {
                    sidebar.classList.remove('sidebar-closed');
                    overlay.classList.add('hidden');
                    overlay.style.display = 'none';
                    document.body.style.overflow = '';
                } else {
                    // On mobile, start with sidebar closed
                    sidebar.classList.add('sidebar-closed');
                }

                // Update user info display based on screen size
                if (currentUser) {
                    updateUserHeader();
                }
            });

            // Initialize sidebar state
            if (window.innerWidth < 1024) {
                sidebar.classList.add('sidebar-closed');
                overlay.classList.add('hidden');
                overlay.style.display = 'none';
            }

            // Handle escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && window.innerWidth < 1024) {
                    closeSidebar();
                }
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();

            // Initialize sidebar
            initializeSidebar();

            // Check authentication first
            checkAuthentication().then(isAuthenticated => {
                if (isAuthenticated) {
                    loadRealSocialAccounts();
                    loadRealDashboard();
                    loadMonitoringStatus();
                    loadModelStatus();

                    // Initialize filtering system
                    initializeFiltering();

                    // Setup infinite scroll and search
                    setupInfiniteScroll();
                    setupSearch();

                    // Setup sticky search bar
                    setupStickySearchBar();

                    // Start background status monitoring
                    startBackgroundStatusMonitoring();
                }
            });
        });

        function startBackgroundStatusMonitoring() {
            // Initial status check
            setTimeout(() => {
                checkBackendStatus();
                updateOverallStatus();
            }, 2000);

            // Periodic status check every 2 minutes
            setInterval(async () => {
                await checkBackendStatus();
                updateOverallStatus();
            }, 120000);
        }

        // System Action Functions
        async function startBackendService() {
            console.log('🚀 Starting backend service...');
            // In a real implementation, this would call a system management API
            alert('Backend service start command sent. Please check the system status in a few seconds.');
            setTimeout(checkBackendStatus, 3000);
        }

        async function stopBackendService() {
            console.log('🛑 Stopping backend service...');
            // In a real implementation, this would call a system management API
            if (confirm('Are you sure you want to stop the backend service? This will disable all API functionality.')) {
                alert('Backend service stop command sent.');
                setTimeout(checkBackendStatus, 2000);
            }
        }

        async function startFullSystem() {
            console.log('🚀 Starting full system...');
            if (confirm('This will start all system services. Continue?')) {
                alert('Full system startup initiated. This may take 30-60 seconds.');
                // Simulate system startup
                setTimeout(() => {
                    refreshSystemStatus();
                }, 5000);
            }
        }

        async function stopFullSystem() {
            console.log('🛑 Stopping full system...');
            if (confirm('This will stop ALL system services. Are you sure?')) {
                alert('Full system shutdown initiated.');
                // Simulate system shutdown
                setTimeout(() => {
                    refreshSystemStatus();
                }, 3000);
            }
        }

        async function restartFullSystem() {
            console.log('🔄 Restarting full system...');
            if (confirm('This will restart all system services. Continue?')) {
                alert('Full system restart initiated. This may take 1-2 minutes.');
                setTimeout(() => {
                    refreshSystemStatus();
                }, 10000);
            }
        }

        function openBackendLogs() {
            const logsContainer = document.getElementById('logs-container');
            if (logsContainer) {
                logsContainer.scrollIntoView({ behavior: 'smooth' });
                document.getElementById('log-source').value = 'backend';
                refreshLogs();
            }
        }

        function openAdminUILogs() {
            const logsContainer = document.getElementById('logs-container');
            if (logsContainer) {
                logsContainer.scrollIntoView({ behavior: 'smooth' });
                document.getElementById('log-source').value = 'adminui';
                refreshLogs();
            }
        }

        function refreshLogs() {
            const logsContainer = document.getElementById('logs-container');
            const logSource = document.getElementById('log-source')?.value || 'backend';

            if (!logsContainer) return;

            // Mock log data
            const mockLogs = {
                backend: [
                    '[2024-06-06 23:15:32] INFO: Backend service started successfully',
                    '[2024-06-06 23:15:33] INFO: Database connection established',
                    '[2024-06-06 23:15:34] INFO: Phi-3 Mini model loaded',
                    '[2024-06-06 23:15:35] INFO: API endpoints registered',
                    '[2024-06-06 23:15:36] INFO: Health check endpoint responding',
                    '[2024-06-06 23:16:45] INFO: Processing new response request',
                    '[2024-06-06 23:16:46] INFO: AI model generated response successfully',
                    '[2024-06-06 23:17:12] INFO: Response saved to database',
                ],
                adminui: [
                    '[2024-06-06 23:15:30] INFO: Admin UI server started on port 3000',
                    '[2024-06-06 23:15:31] INFO: Static files served from admin-ui/',
                    '[2024-06-06 23:16:20] INFO: User accessed dashboard',
                    '[2024-06-06 23:17:45] INFO: User viewed response management',
                    '[2024-06-06 23:18:30] INFO: User accessed system status',
                ],
                system: [
                    '[2024-06-06 23:15:25] INFO: System startup initiated',
                    '[2024-06-06 23:15:26] INFO: Virtual environment activated',
                    '[2024-06-06 23:15:27] INFO: Dependencies verified',
                    '[2024-06-06 23:15:28] INFO: Database schema validated',
                    '[2024-06-06 23:15:29] INFO: All prerequisites met',
                    '[2024-06-06 23:15:30] INFO: Services starting...',
                ]
            };

            const logs = mockLogs[logSource] || mockLogs.backend;
            logsContainer.innerHTML = logs.map(log => `<div>${log}</div>`).join('');
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function clearLogs() {
            const logsContainer = document.getElementById('logs-container');
            if (logsContainer && confirm('Clear all logs?')) {
                logsContainer.innerHTML = '<div class="text-gray-500">Logs cleared.</div>';
            }
        }

        function downloadSystemLogs() {
            // Create a mock log file
            const logContent = `
Agentic ORM System - Log Export
Generated: ${new Date().toISOString()}

=== BACKEND LOGS ===
[2024-06-06 23:15:32] INFO: Backend service started successfully
[2024-06-06 23:15:33] INFO: Database connection established
[2024-06-06 23:15:34] INFO: Phi-3 Mini model loaded
[2024-06-06 23:15:35] INFO: API endpoints registered

=== ADMIN UI LOGS ===
[2024-06-06 23:15:30] INFO: Admin UI server started on port 3000
[2024-06-06 23:15:31] INFO: Static files served from admin-ui/

=== SYSTEM LOGS ===
[2024-06-06 23:15:25] INFO: System startup initiated
[2024-06-06 23:15:26] INFO: Virtual environment activated
            `.trim();

            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `agentic-orm-logs-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // More User Management Functions
        async function createUser(event) {
            event.preventDefault();
            const formData = new FormData(event.target);

            const userData = {
                username: formData.get('username'),
                email: formData.get('email'),
                password: formData.get('password'),
                role: formData.get('role'),
                full_name: formData.get('full_name')
            };

            // Get selected social accounts for marketing users
            if (userData.role === 'marketing') {
                const selectedAccounts = Array.from(document.querySelectorAll('#social-accounts-list input[type="checkbox"]:checked'))
                    .map(cb => parseInt(cb.value));
                userData.social_account_ids = selectedAccounts;
            }

            try {
                const response = await fetch(`${API_BASE}/api/v1/users/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${sessionToken}`
                    },
                    body: JSON.stringify(userData)
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    hideCreateUserModal();
                    loadUsers();
                    showNotification('User created successfully!', 'success');
                    event.target.reset();
                } else {
                    showNotification(data.error || 'Failed to create user', 'error');
                }
            } catch (error) {
                console.error('Error creating user:', error);
                showNotification('Error creating user', 'error');
            }
        }

        async function deleteUser(userId, username) {
            if (username === 'admin') {
                showNotification('Cannot delete the default admin user', 'error');
                return;
            }

            if (!confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/v1/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${sessionToken}`
                    }
                });

                if (response.ok) {
                    loadUsers();
                    showNotification('User deleted successfully', 'success');
                } else {
                    const data = await response.json();
                    showNotification(data.error || 'Failed to delete user', 'error');
                }
            } catch (error) {
                console.error('Error deleting user:', error);
                showNotification('Error deleting user', 'error');
            }
        }

        async function assignSocialAccounts(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const userId = formData.get('user_id');

            const selectedAccounts = Array.from(document.querySelectorAll('#assign-social-accounts-list input[type="checkbox"]:checked'))
                .map(cb => parseInt(cb.value));

            try {
                const response = await fetch(`${API_BASE}/api/v1/users/${userId}/social-accounts`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${sessionToken}`
                    },
                    body: JSON.stringify({ social_account_ids: selectedAccounts })
                });

                if (response.ok) {
                    hideAssignAccountsModal();
                    loadUsers();
                    showNotification('Social accounts assigned successfully!', 'success');
                } else {
                    const data = await response.json();
                    showNotification(data.error || 'Failed to assign accounts', 'error');
                }
            } catch (error) {
                console.error('Error assigning accounts:', error);
                showNotification('Error assigning accounts', 'error');
            }
        }

        // Enhanced User Management Functions
        async function showEditUserModal(userId) {
            try {
                const response = await fetch(`${API_BASE}/api/v1/users/${userId}`, {
                    headers: {
                        'Authorization': `Bearer ${sessionToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const user = data.user;

                    // Populate form fields
                    document.getElementById('edit-user-id').value = user.id;
                    document.getElementById('edit-full-name').value = user.full_name || '';
                    document.getElementById('edit-username').value = user.username || '';
                    document.getElementById('edit-email').value = user.email || '';
                    document.getElementById('edit-role').value = user.role || '';
                    document.getElementById('edit-job-title').value = user.job_title || '';
                    document.getElementById('edit-department').value = user.department || '';
                    document.getElementById('edit-is-active').checked = user.is_active || false;

                    // Update avatar
                    const avatar = document.getElementById('edit-user-avatar');
                    if (user.profile_image) {
                        avatar.innerHTML = `<img src="${user.profile_image}" alt="Profile" class="w-20 h-20 rounded-full object-cover">`;
                    } else {
                        avatar.innerHTML = `<span class="text-white text-2xl font-medium">${user.full_name.charAt(0)}</span>`;
                    }

                    // Handle social accounts section
                    toggleEditSocialAccountsSection(user.role);
                    if (user.role === 'marketing') {
                        loadSocialAccountsForAssignment(true, user.assigned_accounts);
                    }

                    document.getElementById('edit-user-modal').classList.remove('hidden');
                } else {
                    showNotification('Failed to load user details', 'error');
                }
            } catch (error) {
                console.error('Error loading user:', error);
                showNotification('Error loading user details', 'error');
            }
        }

        function hideEditUserModal() {
            document.getElementById('edit-user-modal').classList.add('hidden');
        }

        async function updateUser(event) {
            event.preventDefault();
            const formData = new FormData(event.target);

            const userData = {
                full_name: formData.get('full_name'),
                username: formData.get('username'),
                email: formData.get('email'),
                role: formData.get('role'),
                job_title: formData.get('job_title'),
                department: formData.get('department'),
                is_active: formData.get('is_active') === 'on'
            };

            const userId = formData.get('user_id');

            try {
                const response = await fetch(`${API_BASE}/api/v1/users/${userId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${sessionToken}`
                    },
                    body: JSON.stringify(userData)
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    hideEditUserModal();
                    loadUsers();
                    showNotification('User updated successfully!', 'success');
                } else {
                    showNotification(data.error || 'Failed to update user', 'error');
                }
            } catch (error) {
                console.error('Error updating user:', error);
                showNotification('Error updating user', 'error');
            }
        }

        async function resetUserPassword(userId, username) {
            const newPassword = prompt(`Enter new password for ${username}:`);
            if (!newPassword || newPassword.length < 6) {
                showNotification('Password must be at least 6 characters', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/v1/users/${userId}/reset-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${sessionToken}`
                    },
                    body: JSON.stringify({ new_password: newPassword })
                });

                if (response.ok) {
                    showNotification('Password reset successfully', 'success');
                } else {
                    const data = await response.json();
                    showNotification(data.error || 'Failed to reset password', 'error');
                }
            } catch (error) {
                console.error('Error resetting password:', error);
                showNotification('Error resetting password', 'error');
            }
        }

        function toggleEditSocialAccountsSection(role) {
            const section = document.getElementById('edit-social-accounts-section');
            if (role === 'marketing') {
                section.classList.remove('hidden');
            } else {
                section.classList.add('hidden');
            }
        }

        function previewEditImage(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('edit-user-avatar').innerHTML =
                        `<img src="${e.target.result}" alt="Profile" class="w-20 h-20 rounded-full object-cover">`;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Company Settings Functions
        function showCompanySettingsModal() {
            loadCompanySettings();
            document.getElementById('company-settings-modal').classList.remove('hidden');
        }

        function hideCompanySettingsModal() {
            document.getElementById('company-settings-modal').classList.add('hidden');
        }

        async function loadCompanySettings() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/company/settings`, {
                    headers: {
                        'Authorization': `Bearer ${sessionToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const settings = data.settings;

                    document.getElementById('company-name').value = settings.company_name || '';
                    document.getElementById('company-industry').value = settings.company_industry || '';
                    document.getElementById('header-title').value = settings.header_title || '🤖 Agentic ORM';

                    if (settings.company_logo) {
                        document.getElementById('company-logo-preview').innerHTML =
                            `<img src="${settings.company_logo}" alt="Logo" class="w-16 h-16 rounded-lg object-cover">`;
                    }
                }
            } catch (error) {
                console.error('Error loading company settings:', error);
            }
        }

        async function updateCompanySettings(event) {
            event.preventDefault();
            const formData = new FormData(event.target);

            const settings = {
                company_name: formData.get('company_name'),
                company_industry: formData.get('company_industry'),
                header_title: formData.get('header_title')
            };

            try {
                const response = await fetch(`${API_BASE}/api/v1/company/settings`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${sessionToken}`
                    },
                    body: JSON.stringify(settings)
                });

                if (response.ok) {
                    hideCompanySettingsModal();
                    showNotification('Company settings updated successfully!', 'success');

                    // Update header title immediately
                    const headerTitle = document.querySelector('h1');
                    if (headerTitle) {
                        headerTitle.textContent = settings.header_title;
                    }
                } else {
                    const data = await response.json();
                    showNotification(data.error || 'Failed to update settings', 'error');
                }
            } catch (error) {
                console.error('Error updating company settings:', error);
                showNotification('Error updating company settings', 'error');
            }
        }

        function previewCompanyLogo(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('company-logo-preview').innerHTML =
                        `<img src="${e.target.result}" alt="Logo" class="w-16 h-16 rounded-lg object-cover">`;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        function removeCompanyLogo() {
            document.getElementById('company-logo-preview').innerHTML =
                '<i data-lucide="building" class="w-8 h-8 text-gray-400"></i>';
            document.getElementById('company-logo').value = '';
            lucide.createIcons();
        }

        // Modal Functions

        function showCreateUserModal() {
            document.getElementById('create-user-modal').classList.remove('hidden');
            loadSocialAccountsForAssignment();
        }

        function hideCreateUserModal() {
            document.getElementById('create-user-modal').classList.add('hidden');
        }

        function showAssignAccountsModal(userId, userName) {
            document.getElementById('assign-user-id').value = userId;
            document.getElementById('assign-user-name').textContent = userName;
            document.getElementById('assign-accounts-modal').classList.remove('hidden');
            loadSocialAccountsForAssignment(true);
        }

        function hideAssignAccountsModal() {
            document.getElementById('assign-accounts-modal').classList.add('hidden');
        }

        function toggleSocialAccountsSection(role) {
            const section = document.getElementById('social-accounts-section');
            if (role === 'marketing') {
                section.classList.remove('hidden');
                loadSocialAccountsForAssignment();
            } else {
                section.classList.add('hidden');
            }
        }

        async function loadSocialAccountsForAssignment(isAssignModal = false) {
            try {
                const response = await fetch(`${API_BASE}/api/v1/social-accounts/`);
                const data = await response.json();

                const containerId = isAssignModal ? 'assign-social-accounts-list' : 'social-accounts-list';
                const container = document.getElementById(containerId);
                container.innerHTML = '';

                data.accounts.forEach(account => {
                    const checkbox = document.createElement('div');
                    checkbox.className = 'flex items-center';
                    checkbox.innerHTML = `
                        <input type="checkbox" value="${account.id}" id="${containerId}-${account.id}"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="${containerId}-${account.id}" class="ml-2 text-sm text-gray-700">
                            <span class="font-medium">@${account.username}</span>
                            <span class="text-gray-500">(${account.platform})</span>
                        </label>
                    `;
                    container.appendChild(checkbox);
                });
            } catch (error) {
                console.error('Error loading social accounts:', error);
            }
        }

        function refreshUsers() {
            loadUsers();
        }

        function hasPermission(permission) {
            if (!currentUser) return false;

            const rolePermissions = {
                admin: [
                    'users.view', 'users.create', 'users.edit', 'users.delete',
                    'credentials.view', 'credentials.create', 'credentials.edit', 'credentials.delete'
                ],
                infra: [],
                marketing: []
            };

            return rolePermissions[currentUser.role]?.includes(permission) || false;
        }

        function showNoSocialAccountsMessage() {
            const list = document.getElementById('responses-list');
            list.innerHTML = `
                <div class="flex flex-col items-center justify-center py-16 px-4">
                    <div class="text-center max-w-md">
                        <div class="w-16 h-16 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center">
                            <i data-lucide="alert-triangle" class="w-8 h-8 text-yellow-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No Social Accounts Assigned</h3>
                        <p class="text-gray-600 mb-6">
                            You don't have any social media accounts assigned to your profile.
                            Please contact your administrator to assign social accounts before you can manage responses.
                        </p>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-start">
                                <i data-lucide="info" class="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"></i>
                                <div class="text-sm text-blue-800">
                                    <p class="font-medium mb-1">Need Help?</p>
                                    <p>Contact your system administrator to:</p>
                                    <ul class="list-disc list-inside mt-2 space-y-1">
                                        <li>Assign Twitter/social media accounts to your profile</li>
                                        <li>Configure your response management permissions</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Re-initialize icons
            lucide.createIcons();
        }

        // My Profile Functions
        function loadMyProfile() {
            if (!currentUser) return;

            // Update profile display
            document.getElementById('profile-display-name').textContent = currentUser.full_name || 'Unknown User';
            document.getElementById('profile-username').textContent = `@${currentUser.username || 'unknown'}`;
            document.getElementById('profile-email').textContent = currentUser.email || 'No email';

            // Update avatar
            const avatarText = document.getElementById('profile-avatar-text');
            avatarText.textContent = (currentUser.full_name || 'U').charAt(0).toUpperCase();

            // Update role badge
            const roleBadge = document.getElementById('profile-role-badge');
            const roleColors = {
                admin: 'bg-green-100 text-green-800',
                infra: 'bg-purple-100 text-purple-800',
                marketing: 'bg-orange-100 text-orange-800'
            };
            roleBadge.className = `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${roleColors[currentUser.role] || 'bg-gray-100 text-gray-800'}`;
            roleBadge.textContent = currentUser.role ? currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1) : 'Unknown';

            // Update detailed information
            document.getElementById('profile-full-name').textContent = currentUser.full_name || 'Not set';
            document.getElementById('profile-username-detail').textContent = `@${currentUser.username || 'unknown'}`;
            document.getElementById('profile-email-detail').textContent = currentUser.email || 'Not set';
            document.getElementById('profile-role-detail').textContent = currentUser.role ? currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1) : 'Unknown';

            // Update account information
            if (currentUser.created_at) {
                document.getElementById('profile-created-at').textContent = new Date(currentUser.created_at).toLocaleDateString();
            }
            if (currentUser.last_login) {
                document.getElementById('profile-last-login').textContent = new Date(currentUser.last_login).toLocaleDateString();
            }

            // Show social accounts section for marketing users
            if (currentUser.role === 'marketing') {
                const socialAccountsSection = document.getElementById('profile-social-accounts-section');
                socialAccountsSection.classList.remove('hidden');
                loadProfileSocialAccounts();
            }
        }

        function loadProfileSocialAccounts() {
            const container = document.getElementById('profile-social-accounts-list');

            if (!currentUser.assigned_accounts || currentUser.assigned_accounts.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <div class="w-12 h-12 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center">
                            <i data-lucide="alert-triangle" class="w-6 h-6 text-yellow-600"></i>
                        </div>
                        <p class="text-gray-600">No social accounts assigned to your profile.</p>
                        <p class="text-sm text-gray-500 mt-1">Contact your administrator to assign social accounts.</p>
                    </div>
                `;
            } else {
                container.innerHTML = currentUser.assigned_accounts.map(account => `
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <i data-lucide="twitter" class="w-4 h-4 text-blue-600"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">@${account.username}</p>
                                <p class="text-sm text-gray-500">${account.platform}</p>
                            </div>
                        </div>
                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                            Active
                        </span>
                    </div>
                `).join('');
            }

            lucide.createIcons();
        }

        function showEditProfileModal() {
            if (!currentUser) return;

            // Populate form with current data
            document.getElementById('edit-profile-full-name').value = currentUser.full_name || '';
            document.getElementById('edit-profile-username').value = currentUser.username || '';
            document.getElementById('edit-profile-email').value = currentUser.email || '';

            document.getElementById('edit-profile-modal').classList.remove('hidden');
        }

        function hideEditProfileModal() {
            document.getElementById('edit-profile-modal').classList.add('hidden');
        }

        async function updateProfile(event) {
            event.preventDefault();
            const formData = new FormData(event.target);

            const profileData = {
                full_name: formData.get('full_name'),
                username: formData.get('username'),
                email: formData.get('email')
            };

            try {
                const response = await fetch(`${API_BASE}/api/v1/users/${currentUser.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${sessionToken}`
                    },
                    body: JSON.stringify(profileData)
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    // Update current user data
                    currentUser.full_name = profileData.full_name;
                    currentUser.username = profileData.username;
                    currentUser.email = profileData.email;

                    hideEditProfileModal();
                    loadMyProfile();
                    updateUserHeader(); // Update header display
                    showNotification('Profile updated successfully!', 'success');
                } else {
                    showNotification(data.error || 'Failed to update profile', 'error');
                }
            } catch (error) {
                console.error('Error updating profile:', error);
                showNotification('Error updating profile', 'error');
            }
        }

        function showChangePasswordModal() {
            document.getElementById('change-password-modal').classList.remove('hidden');
        }

        function hideChangePasswordModal() {
            document.getElementById('change-password-modal').classList.add('hidden');
            // Clear form
            document.getElementById('change-password-form').reset();
        }

        async function changePassword(event) {
            event.preventDefault();
            const formData = new FormData(event.target);

            const currentPassword = formData.get('current_password');
            const newPassword = formData.get('new_password');
            const confirmPassword = formData.get('confirm_password');

            // Validate passwords match
            if (newPassword !== confirmPassword) {
                showNotification('New passwords do not match', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/v1/users/${currentUser.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${sessionToken}`
                    },
                    body: JSON.stringify({
                        current_password: currentPassword,
                        password: newPassword
                    })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    hideChangePasswordModal();
                    showNotification('Password changed successfully!', 'success');
                } else {
                    showNotification(data.error || 'Failed to change password', 'error');
                }
            } catch (error) {
                console.error('Error changing password:', error);
                showNotification('Error changing password', 'error');
            }
        }

        function showNotification(message, type = 'info') {
            // Simple notification system
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Credential Management Functions
        async function loadCredentials() {
            if (!hasPermission('credentials.view')) return;

            try {
                const response = await fetch(`${API_BASE}/api/v1/credentials/`, {
                    headers: {
                        'Authorization': `Bearer ${sessionToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayCredentials(data.credentials);
                } else {
                    showNotification('Failed to load credentials', 'error');
                }
            } catch (error) {
                console.error('Error loading credentials:', error);
                showNotification('Error loading credentials', 'error');
            }
        }

        function displayCredentials(credentials) {
            const tbody = document.getElementById('credentials-table-body');
            tbody.innerHTML = '';

            if (credentials.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            No credentials found. Add your first credential to get started.
                        </td>
                    </tr>
                `;
                return;
            }

            credentials.forEach(credential => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';

                const platformColors = {
                    twitter: 'bg-blue-100 text-blue-800',
                    instagram: 'bg-pink-100 text-pink-800',
                    facebook: 'bg-indigo-100 text-indigo-800',
                    linkedin: 'bg-blue-100 text-blue-800',
                    system: 'bg-gray-100 text-gray-800'
                };

                const statusColor = credential.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                const validationColor = credential.is_validated ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                                <i data-lucide="shield" class="w-5 h-5 text-gray-600"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">${credential.name}</div>
                                <div class="text-sm text-gray-500">${credential.description || 'No description'}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${platformColors[credential.platform]}">
                            ${credential.platform.charAt(0).toUpperCase() + credential.platform.slice(1)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${statusColor}">
                            ${credential.is_active ? 'Active' : 'Inactive'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${validationColor}">
                            ${credential.is_validated ? 'Validated' : 'Pending'}
                        </span>
                        ${credential.last_validated_at ?
                            `<div class="text-xs text-gray-500 mt-1">${new Date(credential.last_validated_at).toLocaleDateString()}</div>` :
                            ''
                        }
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${credential.created_at ? new Date(credential.created_at).toLocaleDateString() : '-'}
                        ${credential.created_by ? `<br><span class="text-xs">by ${credential.created_by}</span>` : ''}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            <button onclick="showEditCredentialModal(${credential.id})"
                                    class="text-blue-600 hover:text-blue-900" title="Edit Credential">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button onclick="validateCredential(${credential.id})"
                                    class="text-green-600 hover:text-green-900" title="Validate Credential">
                                <i data-lucide="check-circle" class="w-4 h-4"></i>
                            </button>
                            <button onclick="deleteCredential(${credential.id}, '${credential.name}')"
                                    class="text-red-600 hover:text-red-900" title="Delete Credential">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });

            // Re-initialize icons
            lucide.createIcons();
        }

        async function addCredential(event) {
            event.preventDefault();
            const formData = new FormData(event.target);

            const platform = formData.get('platform');
            const credentials = {};

            // Build credentials object based on platform
            if (platform === 'twitter') {
                credentials.bearer_token = formData.get('bearer_token');
                credentials.api_key = formData.get('api_key');
                credentials.api_secret = formData.get('api_secret');
                credentials.access_token = formData.get('access_token');
                credentials.access_token_secret = formData.get('access_token_secret');
            } else if (platform === 'instagram') {
                credentials.access_token = formData.get('instagram_access_token');
                credentials.app_id = formData.get('app_id');
                credentials.app_secret = formData.get('app_secret');
            }

            const credentialData = {
                name: formData.get('name'),
                platform: platform,
                description: formData.get('description'),
                credentials: credentials
            };

            try {
                const response = await fetch(`${API_BASE}/api/v1/credentials/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${sessionToken}`
                    },
                    body: JSON.stringify(credentialData)
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    hideAddCredentialModal();
                    loadCredentials();
                    showNotification('Credentials added successfully!', 'success');
                    event.target.reset();
                } else {
                    showNotification(data.error || 'Failed to add credentials', 'error');
                }
            } catch (error) {
                console.error('Error adding credentials:', error);
                showNotification('Error adding credentials', 'error');
            }
        }

        async function deleteCredential(credentialId, name) {
            if (!confirm(`Are you sure you want to delete credential "${name}"? This action cannot be undone.`)) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/v1/credentials/${credentialId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${sessionToken}`
                    }
                });

                if (response.ok) {
                    loadCredentials();
                    showNotification('Credential deleted successfully', 'success');
                } else {
                    const data = await response.json();
                    showNotification(data.error || 'Failed to delete credential', 'error');
                }
            } catch (error) {
                console.error('Error deleting credential:', error);
                showNotification('Error deleting credential', 'error');
            }
        }

        function showAddCredentialModal() {
            document.getElementById('add-credential-modal').classList.remove('hidden');
        }

        function hideAddCredentialModal() {
            document.getElementById('add-credential-modal').classList.add('hidden');
        }

        function updateCredentialFields(platform) {
            // Hide all credential fields
            document.querySelectorAll('.credential-fields').forEach(field => {
                field.classList.add('hidden');
            });

            // Show relevant fields
            if (platform) {
                const fieldsDiv = document.getElementById(`${platform}-fields`);
                if (fieldsDiv) {
                    fieldsDiv.classList.remove('hidden');
                }
            }
        }

        function refreshCredentials() {
            loadCredentials();
        }

        async function showEditCredentialModal(credentialId) {
            try {
                const response = await fetch(`${API_BASE}/api/v1/credentials/${credentialId}`, {
                    headers: {
                        'Authorization': `Bearer ${sessionToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const credential = data.credential;

                    // Populate form fields
                    document.getElementById('edit-credential-id').value = credential.id;
                    document.getElementById('edit-credential-name').value = credential.name;
                    document.getElementById('edit-credential-platform').value = credential.platform;
                    document.getElementById('edit-credential-description').value = credential.description || '';
                    document.getElementById('edit-credential-active').checked = credential.is_active;

                    // Show masked current credentials
                    displayCurrentCredentials(credential.masked_credentials);

                    // Show relevant credential fields
                    updateEditCredentialFields(credential.platform);

                    document.getElementById('edit-credential-modal').classList.remove('hidden');
                } else {
                    showNotification('Failed to load credential details', 'error');
                }
            } catch (error) {
                console.error('Error loading credential:', error);
                showNotification('Error loading credential details', 'error');
            }
        }

        function hideEditCredentialModal() {
            document.getElementById('edit-credential-modal').classList.add('hidden');
            // Clear form
            document.getElementById('edit-credential-form').reset();
        }

        function displayCurrentCredentials(maskedCredentials) {
            const container = document.getElementById('current-credentials-display');
            container.innerHTML = '';

            if (maskedCredentials && Object.keys(maskedCredentials).length > 0) {
                Object.entries(maskedCredentials).forEach(([key, value]) => {
                    const credDiv = document.createElement('div');
                    credDiv.className = 'flex justify-between items-center py-1';
                    credDiv.innerHTML = `
                        <span class="font-medium">${key.replace(/_/g, ' ').toUpperCase()}:</span>
                        <span class="font-mono text-gray-500">${value}</span>
                    `;
                    container.appendChild(credDiv);
                });
            } else {
                container.innerHTML = '<span class="text-gray-500">No credentials found</span>';
            }
        }

        function updateEditCredentialFields(platform) {
            // Hide all credential fields
            document.querySelectorAll('.edit-credential-fields').forEach(field => {
                field.classList.add('hidden');
            });

            // Show relevant fields
            if (platform) {
                const fieldsDiv = document.getElementById(`edit-${platform}-fields`);
                if (fieldsDiv) {
                    fieldsDiv.classList.remove('hidden');
                }
            }
        }

        async function updateCredential(event) {
            event.preventDefault();
            const formData = new FormData(event.target);

            const credentialId = formData.get('credential_id');
            const platform = formData.get('platform');

            // Build update data
            const updateData = {
                name: formData.get('name'),
                description: formData.get('description'),
                is_active: formData.get('is_active') === 'on'
            };

            // Build credentials object only with non-empty fields
            const credentials = {};
            let hasCredentialUpdates = false;

            if (platform === 'twitter') {
                const fields = ['bearer_token', 'api_key', 'api_secret', 'access_token', 'access_token_secret'];
                fields.forEach(field => {
                    const value = formData.get(field);
                    if (value && value.trim()) {
                        credentials[field] = value.trim();
                        hasCredentialUpdates = true;
                    }
                });
            } else if (platform === 'instagram') {
                const fields = ['instagram_access_token', 'app_id', 'app_secret'];
                const fieldMap = {
                    'instagram_access_token': 'access_token',
                    'app_id': 'app_id',
                    'app_secret': 'app_secret'
                };
                fields.forEach(field => {
                    const value = formData.get(field);
                    if (value && value.trim()) {
                        credentials[fieldMap[field]] = value.trim();
                        hasCredentialUpdates = true;
                    }
                });
            }

            // Only include credentials if there are updates
            if (hasCredentialUpdates) {
                updateData.credentials = credentials;
            }

            try {
                const response = await fetch(`${API_BASE}/api/v1/credentials/${credentialId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${sessionToken}`
                    },
                    body: JSON.stringify(updateData)
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    hideEditCredentialModal();
                    loadCredentials();
                    showNotification('Credentials updated successfully!', 'success');
                } else {
                    showNotification(data.error || 'Failed to update credentials', 'error');
                }
            } catch (error) {
                console.error('Error updating credentials:', error);
                showNotification('Error updating credentials', 'error');
            }
        }

        async function validateCredential(credentialId) {
            try {
                const response = await fetch(`${API_BASE}/api/v1/credentials/${credentialId}/validate`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${sessionToken}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    if (data.is_valid) {
                        showNotification('Credentials validated successfully!', 'success');
                    } else {
                        showNotification(`Validation failed: ${data.validation_error}`, 'error');
                    }
                    loadCredentials(); // Refresh to show updated validation status
                } else {
                    showNotification('Failed to validate credentials', 'error');
                }
            } catch (error) {
                console.error('Error validating credentials:', error);
                showNotification('Error validating credentials', 'error');
            }
        }
    </script>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Company Info -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                        Agentic ORM
                    </h3>
                    <p class="text-sm text-gray-600">
                        Powered by Microsoft Phi-3 Mini for intelligent, privacy-focused social media automation.
                    </p>
                    <div class="mt-4 flex items-center space-x-2">
                        <div id="footer-status-indicator" class="w-2 h-2 bg-green-400 rounded-full"></div>
                        <span id="footer-status-text" class="text-xs text-gray-500">All systems operational</span>
                        <button onclick="showSection('system-status')" class="text-xs text-blue-600 hover:text-blue-800 ml-2">
                            View Details
                        </button>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                        Quick Links
                    </h3>
                    <ul class="space-y-2">
                        <li>
                            <button onclick="showSection('dashboard')" class="text-sm text-gray-600 hover:text-blue-600 transition-colors">
                                Dashboard
                            </button>
                        </li>
                        <li>
                            <button onclick="showSection('responses')" class="text-sm text-gray-600 hover:text-blue-600 transition-colors">
                                Response Management
                            </button>
                        </li>
                        <li>
                            <button onclick="showSection('social-accounts')" class="text-sm text-gray-600 hover:text-blue-600 transition-colors">
                                Social Accounts
                            </button>
                        </li>
                        <li>
                            <button onclick="showSection('analytics')" class="text-sm text-gray-600 hover:text-blue-600 transition-colors">
                                Analytics
                            </button>
                        </li>
                    </ul>
                </div>

                <!-- System Info -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                        System Information
                    </h3>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div class="flex justify-between">
                            <span>AI Model:</span>
                            <span class="font-medium">Phi-3 Mini</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Data Privacy:</span>
                            <span class="font-medium text-green-600">100% Local</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Version:</span>
                            <span class="font-medium">v1.0.0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Last Updated:</span>
                            <span class="font-medium" id="footer-timestamp">Just now</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="mt-8 pt-8 border-t border-gray-200">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-sm text-gray-500">
                        © 2024 Agentic ORM. Built with privacy and security in mind.
                    </div>
                    <div class="mt-4 md:mt-0 flex items-center space-x-4">
                        <span class="text-xs text-gray-400">Powered by</span>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-blue-600 rounded"></div>
                            <span class="text-xs font-medium text-gray-600">Microsoft Phi-3</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Update footer timestamp
        document.getElementById('footer-timestamp').textContent = new Date().toLocaleString();
    </script>
</body>
</html>
