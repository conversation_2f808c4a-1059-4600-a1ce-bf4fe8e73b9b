<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Learning Dashboard - Agentic ORM</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .card { @apply bg-white rounded-lg shadow-md border border-gray-200 p-6; }
        .btn-primary { @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors; }
        .btn-secondary { @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors; }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            border: 1px solid;
            z-index: 1000;
            max-width: 400px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .notification.success {
            background-color: #d1fae5;
            border-color: #a7f3d0;
            color: #065f46;
        }
        .notification.error {
            background-color: #fee2e2;
            border-color: #fecaca;
            color: #991b1b;
        }
        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .app-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            background: white;
            border-bottom: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            height: 5rem;
        }
        .app-body {
            margin-top: 5rem;
            min-height: calc(100vh - 5rem);
        }
        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            height: 3rem;
        }
        .top-nav-menu {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            height: 100%;
            max-width: 80rem;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .top-nav-item {
            position: relative;
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: #374151;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            border-radius: 0.5rem;
            margin: 0 0.25rem;
        }
        .top-nav-item:hover {
            background-color: #f3f4f6;
            color: #1d4ed8;
        }
        .top-nav-item.active {
            background-color: #dbeafe;
            color: #1d4ed8;
            font-weight: 600;
        }
        .top-nav-item-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }
        .main-content {
            flex: 1;
            background: #f9fafb;
            width: 100%;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="flex items-center justify-between h-20 px-6">
                <div class="flex items-center space-x-4">
                    <button id="hamburger" class="hamburger lg:hidden p-2 rounded-md hover:bg-gray-100">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="brain" class="w-8 h-8 text-blue-600"></i>
                        <h1 class="text-xl font-bold text-gray-900">AI Learning Dashboard</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="refresh-btn" class="btn-secondary flex items-center space-x-2">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        <span>Refresh</span>
                    </button>
                    <button id="add-rule-btn" class="btn-primary flex items-center space-x-2">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                        <span>Add Rule</span>
                    </button>
                </div>
            </div>

            <!-- Top Navigation -->
            <nav class="top-nav">
                <div class="top-nav-menu">
                    <a href="dashboard.html" class="top-nav-item">
                        <i data-lucide="home" class="top-nav-item-icon"></i>
                        Dashboard
                    </a>
                    <a href="responses.html" class="top-nav-item">
                        <i data-lucide="message-square" class="top-nav-item-icon"></i>
                        Responses
                    </a>
                    <a href="learning.html" class="top-nav-item active">
                        <i data-lucide="brain" class="top-nav-item-icon"></i>
                        AI Learning
                    </a>
                    <a href="reporting.html" class="top-nav-item">
                        <i data-lucide="bar-chart-3" class="top-nav-item-icon"></i>
                        Reports
                    </a>
                    <a href="settings.html" class="top-nav-item">
                        <i data-lucide="settings" class="top-nav-item-icon"></i>
                        Settings
                    </a>
                </div>
            </nav>
        </header>

        <!-- Main Content -->
        <div class="app-body">
            <main class="main-content p-6">
                <!-- Loading State -->
                <div id="loading" class="flex items-center justify-center h-64">
                    <i data-lucide="loader-2" class="w-8 h-8 animate-spin mr-2"></i>
                    <span>Loading learning data...</span>
                </div>

                <!-- Main Content -->
                <div id="content" class="hidden space-y-6">
                    <!-- Overview Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Active Learning Patterns</p>
                                    <p id="patterns-count" class="text-2xl font-bold text-gray-900">0</p>
                                    <p class="text-xs text-gray-500">Patterns learned from feedback</p>
                                </div>
                                <i data-lucide="trending-up" class="w-8 h-8 text-blue-600"></i>
                            </div>
                        </div>

                        <div class="card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Languages Supported</p>
                                    <p id="languages-count" class="text-2xl font-bold text-gray-900">0</p>
                                    <p class="text-xs text-gray-500">Active multilingual learning</p>
                                </div>
                                <i data-lucide="globe" class="w-8 h-8 text-green-600"></i>
                            </div>
                        </div>

                        <div class="card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Custom Rules</p>
                                    <p id="rules-count" class="text-2xl font-bold text-gray-900">0</p>
                                    <p class="text-xs text-gray-500">User-defined guidelines</p>
                                </div>
                                <i data-lucide="users" class="w-8 h-8 text-purple-600"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Language Performance -->
                    <div id="language-performance" class="card hidden">
                        <h3 class="text-lg font-semibold mb-4">Language Performance</h3>
                        <div id="language-stats" class="space-y-3">
                            <!-- Language stats will be populated here -->
                        </div>
                    </div>

                    <!-- Custom Rules -->
                    <div class="card">
                        <h3 class="text-lg font-semibold mb-4">Custom Rules & Guidelines</h3>
                        <div id="custom-rules" class="space-y-3">
                            <div class="text-center py-8 text-gray-500">
                                <i data-lucide="alert-circle" class="w-8 h-8 mx-auto mb-2"></i>
                                <p>No custom rules defined yet.</p>
                                <p class="text-sm">Create rules to guide AI behavior for specific scenarios.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Context Performance -->
                    <div id="context-performance" class="card hidden">
                        <h3 class="text-lg font-semibold mb-4">Context Performance Analysis</h3>
                        <div id="context-stats" class="space-y-3">
                            <!-- Context stats will be populated here -->
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Rule Modal -->
    <div id="add-rule-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold">Create Custom Rule</h2>
                <button id="close-modal" class="text-gray-400 hover:text-gray-600">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            
            <form id="rule-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Rule Name</label>
                    <input type="text" id="rule-name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="e.g., VIP Customer Priority">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea id="rule-description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Describe what this rule does..."></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Condition</label>
                    <input type="text" id="rule-condition" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="e.g., customer_tier == 'Platinum' AND sentiment == 'negative'">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Action</label>
                    <textarea id="rule-action" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="e.g., Use empathetic tone, mention compensation, escalate to manager"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                    <select id="rule-priority" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="1">Low (1)</option>
                        <option value="2">Medium (2)</option>
                        <option value="3">High (3)</option>
                        <option value="4">Critical (4)</option>
                    </select>
                </div>
                
                <div class="flex justify-end space-x-2 pt-4">
                    <button type="button" id="cancel-rule" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Create Rule</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        let learningData = null;
        let customRules = [];

        // DOM elements
        const loading = document.getElementById('loading');
        const content = document.getElementById('content');
        const addRuleModal = document.getElementById('add-rule-modal');
        const ruleForm = document.getElementById('rule-form');

        // Fetch learning data
        async function fetchLearningData() {
            try {
                const [patternsRes, rulesRes] = await Promise.all([
                    fetch('/api/v1/learning/patterns'),
                    fetch('/api/v1/learning/rules')
                ]);
                
                const patterns = await patternsRes.json();
                const rules = await rulesRes.json();
                
                learningData = patterns;
                customRules = rules.rules || [];
                
                updateUI();
            } catch (error) {
                console.error('Error fetching learning data:', error);
                showNotification('Error loading learning data', 'error');
            } finally {
                loading.classList.add('hidden');
                content.classList.remove('hidden');
            }
        }

        // Update UI with data
        function updateUI() {
            // Update overview cards
            document.getElementById('patterns-count').textContent = 
                learningData?.learning_patterns ? Object.keys(learningData.learning_patterns).length : 0;
            document.getElementById('languages-count').textContent = 
                learningData?.language_stats?.length || 0;
            document.getElementById('rules-count').textContent = customRules.length;

            // Update language performance
            updateLanguagePerformance();
            
            // Update custom rules
            updateCustomRules();
            
            // Update context performance
            updateContextPerformance();
        }

        // Update language performance section
        function updateLanguagePerformance() {
            const container = document.getElementById('language-stats');
            const section = document.getElementById('language-performance');
            
            if (!learningData?.language_stats || learningData.language_stats.length === 0) {
                section.classList.add('hidden');
                return;
            }
            
            section.classList.remove('hidden');
            container.innerHTML = '';
            
            learningData.language_stats.forEach(lang => {
                const langName = {
                    'en': 'English',
                    'ta': 'Tamil', 
                    'hi': 'Hindi',
                    'es': 'Spanish',
                    'fr': 'French'
                }[lang.language] || lang.language;
                
                const div = document.createElement('div');
                div.className = 'flex items-center justify-between p-3 border rounded-lg';
                div.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded">${lang.language.toUpperCase()}</span>
                        <span class="font-medium">${langName}</span>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium">${lang.feedback_count} feedback entries</div>
                        <div class="text-xs text-gray-500">${(lang.avg_effectiveness * 100).toFixed(1)}% effectiveness</div>
                    </div>
                `;
                container.appendChild(div);
            });
        }

        // Update custom rules section
        function updateCustomRules() {
            const container = document.getElementById('custom-rules');
            
            if (customRules.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i data-lucide="alert-circle" class="w-8 h-8 mx-auto mb-2"></i>
                        <p>No custom rules defined yet.</p>
                        <p class="text-sm">Create rules to guide AI behavior for specific scenarios.</p>
                    </div>
                `;
                lucide.createIcons();
                return;
            }
            
            container.innerHTML = '';
            
            customRules.forEach(rule => {
                const div = document.createElement('div');
                div.className = 'border rounded-lg p-4';
                div.innerHTML = `
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center space-x-2">
                            <h4 class="font-medium">${rule.rule_name}</h4>
                            <span class="px-2 py-1 text-xs font-medium rounded ${rule.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">${rule.active ? 'Active' : 'Inactive'}</span>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">Priority ${rule.priority}</span>
                        </div>
                        ${rule.active ? '<i data-lucide="check-circle" class="w-4 h-4 text-green-500"></i>' : ''}
                    </div>
                    <p class="text-sm text-gray-600 mb-2">${rule.rule_description}</p>
                    <div class="text-xs space-y-1">
                        <div><strong>Condition:</strong> ${rule.rule_condition}</div>
                        <div><strong>Action:</strong> ${rule.rule_action}</div>
                    </div>
                `;
                container.appendChild(div);
            });
            
            lucide.createIcons();
        }

        // Update context performance section
        function updateContextPerformance() {
            const container = document.getElementById('context-stats');
            const section = document.getElementById('context-performance');
            
            if (!learningData?.context_stats || learningData.context_stats.length === 0) {
                section.classList.add('hidden');
                return;
            }
            
            section.classList.remove('hidden');
            container.innerHTML = '';
            
            learningData.context_stats.forEach(context => {
                const div = document.createElement('div');
                div.className = 'flex items-center justify-between p-3 border rounded-lg';
                div.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded">${context.context_type}</span>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">${context.customer_tier}</span>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium">${context.count} interactions</div>
                        <div class="text-xs text-gray-500">${(context.effectiveness * 100).toFixed(1)}% success rate</div>
                    </div>
                `;
                container.appendChild(div);
            });
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Event listeners
        document.getElementById('refresh-btn').addEventListener('click', () => {
            loading.classList.remove('hidden');
            content.classList.add('hidden');
            fetchLearningData();
        });

        document.getElementById('add-rule-btn').addEventListener('click', () => {
            addRuleModal.classList.remove('hidden');
            addRuleModal.classList.add('flex');
        });

        document.getElementById('close-modal').addEventListener('click', () => {
            addRuleModal.classList.add('hidden');
            addRuleModal.classList.remove('flex');
        });

        document.getElementById('cancel-rule').addEventListener('click', () => {
            addRuleModal.classList.add('hidden');
            addRuleModal.classList.remove('flex');
        });

        // Handle rule form submission
        ruleForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                rule_name: document.getElementById('rule-name').value,
                rule_description: document.getElementById('rule-description').value,
                rule_condition: document.getElementById('rule-condition').value,
                rule_action: document.getElementById('rule-action').value,
                priority: parseInt(document.getElementById('rule-priority').value)
            };
            
            try {
                const response = await fetch('/api/v1/learning/rules', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                
                if (response.ok) {
                    addRuleModal.classList.add('hidden');
                    addRuleModal.classList.remove('flex');
                    ruleForm.reset();
                    showNotification('Custom rule created successfully', 'success');
                    fetchLearningData();
                } else {
                    showNotification('Error creating rule', 'error');
                }
            } catch (error) {
                console.error('Error creating rule:', error);
                showNotification('Error creating rule', 'error');
            }
        });

        // Initialize
        fetchLearningData();
        
        // Auto-refresh every 30 seconds
        setInterval(fetchLearningData, 30000);
    </script>
</body>
</html>
