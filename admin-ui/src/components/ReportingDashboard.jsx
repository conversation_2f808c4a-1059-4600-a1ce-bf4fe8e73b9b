import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  PieChart, Pie, Cell, LineChart, Line, Area, AreaChart
} from 'recharts';
import { 
  TrendingUp, TrendingDown, Users, AlertTriangle, Globe, 
  MessageSquare, RefreshCw, Download, Filter, Calendar
} from 'lucide-react';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const ReportingDashboard = () => {
  const [userInsights, setUserInsights] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');

  const fetchUserInsights = async () => {
    try {
      const response = await fetch('/api/v1/reporting/user-insights');
      const data = await response.json();
      setUserInsights(data);
    } catch (error) {
      console.error('Error fetching user insights:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserInsights();
    const interval = setInterval(fetchUserInsights, 60000); // Refresh every minute
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading user insights...</span>
      </div>
    );
  }

  const getSentimentColor = (sentiment) => {
    switch (sentiment) {
      case 'positive': return 'text-green-600 bg-green-50';
      case 'negative': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getEngagementColor = (level) => {
    switch (level) {
      case 'high': return 'text-blue-600 bg-blue-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getUrgencyColor = (score) => {
    if (score >= 70) return 'text-red-600 bg-red-50';
    if (score >= 40) return 'text-yellow-600 bg-yellow-50';
    return 'text-green-600 bg-green-50';
  };

  // Prepare chart data
  const sentimentDistribution = userInsights?.user_insights?.reduce((acc, user) => {
    const category = user.sentiment_category;
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {});

  const sentimentChartData = Object.entries(sentimentDistribution || {}).map(([key, value]) => ({
    name: key.charAt(0).toUpperCase() + key.slice(1),
    value,
    color: key === 'positive' ? '#00C49F' : key === 'negative' ? '#FF8042' : '#FFBB28'
  }));

  const languageChartData = userInsights?.language_insights?.map(lang => ({
    language: lang.language === 'en' ? 'English' : 
              lang.language === 'ta' ? 'Tamil' :
              lang.language === 'hi' ? 'Hindi' :
              lang.language === 'es' ? 'Spanish' :
              lang.language === 'fr' ? 'French' : lang.language,
    posts: lang.post_count,
    sentiment: lang.avg_sentiment
  })) || [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Users className="h-6 w-6" />
          <h2 className="text-2xl font-bold">User Insights & Business Intelligence</h2>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={fetchUserInsights} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userInsights?.summary?.total_users || 0}</div>
            <p className="text-xs text-muted-foreground">Active in last 30 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Engagement</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userInsights?.summary?.high_engagement_users || 0}</div>
            <p className="text-xs text-muted-foreground">Users with 5+ interactions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Negative Sentiment</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {userInsights?.summary?.negative_sentiment_users || 0}
            </div>
            <p className="text-xs text-muted-foreground">Require attention</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Multilingual</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userInsights?.summary?.multilingual_users || 0}</div>
            <p className="text-xs text-muted-foreground">Non-English users</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sentiment Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Sentiment Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={sentimentChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {sentimentChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Language Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Language Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={languageChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="language" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="posts" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Trending Issues */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Trending Issues (Last 7 Days)</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {userInsights?.trending_issues?.map((issue, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <Badge variant="outline">{issue.scenario}</Badge>
                  <Badge variant="secondary">{issue.language.toUpperCase()}</Badge>
                  <span className="text-sm">
                    {issue.frequency} mentions
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {issue.negative_count} negative
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {issue.urgency_score}% urgency
                    </div>
                  </div>
                  <Badge className={getUrgencyColor(issue.urgency_score)}>
                    {issue.urgency_score >= 70 ? 'High' : 
                     issue.urgency_score >= 40 ? 'Medium' : 'Low'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* User Details Table */}
      <Card>
        <CardHeader>
          <CardTitle>User Engagement Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {userInsights?.user_insights?.slice(0, 10).map((user, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div>
                    <div className="font-medium">@{user.username}</div>
                    <div className="text-sm text-muted-foreground">{user.display_name}</div>
                  </div>
                  <Badge className={getSentimentColor(user.sentiment_category)}>
                    {user.sentiment_category}
                  </Badge>
                  <Badge className={getEngagementColor(user.engagement_level)}>
                    {user.engagement_level}
                  </Badge>
                  {user.language !== 'en' && (
                    <Badge variant="outline">{user.language.toUpperCase()}</Badge>
                  )}
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">
                    {user.total_posts} posts
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {user.response_rate}% response rate
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Score: {user.avg_sentiment_score}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportingDashboard;
