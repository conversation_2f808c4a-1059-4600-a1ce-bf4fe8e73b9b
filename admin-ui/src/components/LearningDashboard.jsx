import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Brain, TrendingUp, Globe, Users, Plus, RefreshCw, AlertCircle, CheckCircle } from 'lucide-react';

const LearningDashboard = () => {
  const [learningData, setLearningData] = useState(null);
  const [customRules, setCustomRules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddRule, setShowAddRule] = useState(false);
  const [newRule, setNewRule] = useState({
    rule_name: '',
    rule_description: '',
    rule_condition: '',
    rule_action: '',
    priority: 1
  });

  const fetchLearningData = async () => {
    try {
      const [patternsRes, rulesRes] = await Promise.all([
        fetch('/api/v1/learning/patterns'),
        fetch('/api/v1/learning/rules')
      ]);
      
      const patterns = await patternsRes.json();
      const rules = await rulesRes.json();
      
      setLearningData(patterns);
      setCustomRules(rules.rules || []);
    } catch (error) {
      console.error('Error fetching learning data:', error);
    } finally {
      setLoading(false);
    }
  };

  const createRule = async () => {
    try {
      const response = await fetch('/api/v1/learning/rules', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newRule)
      });
      
      if (response.ok) {
        setShowAddRule(false);
        setNewRule({
          rule_name: '',
          rule_description: '',
          rule_condition: '',
          rule_action: '',
          priority: 1
        });
        fetchLearningData();
      }
    } catch (error) {
      console.error('Error creating rule:', error);
    }
  };

  useEffect(() => {
    fetchLearningData();
    const interval = setInterval(fetchLearningData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading learning data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Brain className="h-6 w-6" />
          <h2 className="text-2xl font-bold">AI Learning Dashboard</h2>
        </div>
        <div className="flex space-x-2">
          <Button onClick={fetchLearningData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Dialog open={showAddRule} onOpenChange={setShowAddRule}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Rule
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create Custom Rule</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Rule Name</label>
                  <Input
                    value={newRule.rule_name}
                    onChange={(e) => setNewRule({...newRule, rule_name: e.target.value})}
                    placeholder="e.g., VIP Customer Priority"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <Textarea
                    value={newRule.rule_description}
                    onChange={(e) => setNewRule({...newRule, rule_description: e.target.value})}
                    placeholder="Describe what this rule does..."
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Condition</label>
                  <Input
                    value={newRule.rule_condition}
                    onChange={(e) => setNewRule({...newRule, rule_condition: e.target.value})}
                    placeholder="e.g., customer_tier == 'Platinum' AND sentiment == 'negative'"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Action</label>
                  <Textarea
                    value={newRule.rule_action}
                    onChange={(e) => setNewRule({...newRule, rule_action: e.target.value})}
                    placeholder="e.g., Use empathetic tone, mention compensation, escalate to manager"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Priority</label>
                  <Select value={newRule.priority.toString()} onValueChange={(value) => setNewRule({...newRule, priority: parseInt(value)})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Low (1)</SelectItem>
                      <SelectItem value="2">Medium (2)</SelectItem>
                      <SelectItem value="3">High (3)</SelectItem>
                      <SelectItem value="4">Critical (4)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setShowAddRule(false)}>
                    Cancel
                  </Button>
                  <Button onClick={createRule}>
                    Create Rule
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Learning Patterns Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Learning Patterns</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {learningData?.learning_patterns ? Object.keys(learningData.learning_patterns).length : 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Patterns learned from user feedback
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Languages Supported</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {learningData?.language_stats?.length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Active multilingual learning
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Custom Rules</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{customRules.length}</div>
            <p className="text-xs text-muted-foreground">
              User-defined guidelines
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Language Performance */}
      {learningData?.language_stats && learningData.language_stats.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Language Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {learningData.language_stats.map((lang, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Badge variant="outline">
                      {lang.language.toUpperCase()}
                    </Badge>
                    <span className="font-medium">
                      {lang.language === 'en' ? 'English' : 
                       lang.language === 'ta' ? 'Tamil' :
                       lang.language === 'hi' ? 'Hindi' :
                       lang.language === 'es' ? 'Spanish' :
                       lang.language === 'fr' ? 'French' : lang.language}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {lang.feedback_count} feedback entries
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(lang.avg_effectiveness * 100).toFixed(1)}% effectiveness
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Custom Rules */}
      <Card>
        <CardHeader>
          <CardTitle>Custom Rules & Guidelines</CardTitle>
        </CardHeader>
        <CardContent>
          {customRules.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <AlertCircle className="h-8 w-8 mx-auto mb-2" />
              <p>No custom rules defined yet.</p>
              <p className="text-sm">Create rules to guide AI behavior for specific scenarios.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {customRules.map((rule) => (
                <div key={rule.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">{rule.rule_name}</h4>
                      <Badge variant={rule.active ? "default" : "secondary"}>
                        {rule.active ? "Active" : "Inactive"}
                      </Badge>
                      <Badge variant="outline">Priority {rule.priority}</Badge>
                    </div>
                    {rule.active && <CheckCircle className="h-4 w-4 text-green-500" />}
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">{rule.rule_description}</p>
                  <div className="text-xs space-y-1">
                    <div><strong>Condition:</strong> {rule.rule_condition}</div>
                    <div><strong>Action:</strong> {rule.rule_action}</div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Context Performance */}
      {learningData?.context_stats && learningData.context_stats.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Context Performance Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {learningData.context_stats.map((context, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Badge variant="outline">{context.context_type}</Badge>
                    <Badge variant="secondary">{context.customer_tier}</Badge>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {context.count} interactions
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(context.effectiveness * 100).toFixed(1)}% success rate
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default LearningDashboard;
