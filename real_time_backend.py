#!/usr/bin/env python3
"""
Real-Time Backend Integration
Connects Twitter monitoring to Admin UI with real data
"""

import requests
import os
import json
import sqlite3
import time
from datetime import datetime, timedelta
from dotenv import load_dotenv
import tweepy
from flask import Flask, jsonify, request
from flask_cors import CORS
import asyncio

# Import Phi-3 Mini service
try:
    from phi3_airline_service import Phi3AirlineService, AirlineIncident
    PHI3_AVAILABLE = True
    print("✅ Phi-3 Mini service available")
except ImportError:
    PHI3_AVAILABLE = False
    print("⚠️ Phi-3 Mini not available - using fallback responses")

# Import user management
try:
    from user_management import UserManager, require_auth, require_role, has_permission
    USER_MANAGEMENT_AVAILABLE = True
    print("✅ User management available")
except ImportError:
    USER_MANAGEMENT_AVAILABLE = False
    print("⚠️ User management not available - running without authentication")

# Import secure credential management
try:
    from app.security.credential_manager import get_credential_manager
    SECURE_CREDENTIALS_AVAILABLE = True
    print("✅ Secure credential management available")
except ImportError:
    SECURE_CREDENTIALS_AVAILABLE = False
    print("⚠️ Secure credential management not available - using environment variables")

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)

class RealTimeBackend:
    def __init__(self):
        self.db_path = "social_handler.db"
        self.phi3_service = None

        # Initialize airline policy configuration
        self.airline_policies = {
            'compensation_commitment': False,  # Never commit to compensation without policy review
            'escalation_required': ['baggage_damage', 'flight_delay', 'refund_requests'],
            'response_guidelines': {
                'acknowledge_issue': True,
                'express_empathy': True,
                'request_details': True,
                'avoid_commitments': True,
                'refer_to_policies': True
            }
        }

        # Twitter configuration
        self.twitter_config = {
            'character_limit': 280,  # Default for free accounts
            'pro_character_limit': 25000,  # For Twitter Blue/Pro
            'is_pro_account': False,  # Configurable
            'add_uniqueness': True,  # Add timestamp to avoid duplicate content
            'rate_limit_protection': True,  # Enable rate limiting protection
            'min_delay_between_posts': 300,  # 5 minutes between posts (production: 1800 = 30 min)
            'max_posts_per_hour': 10,  # Conservative limit (production: 3-5)
            'development_mode': True,  # More aggressive limits in dev
            'human_like_delays': True  # Add random delays to seem more human
        }

        self.setup_database()
        self.setup_twitter_client()
        self.setup_phi3_service()
    
    def setup_database(self):
        """Setup SQLite database for real data"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS social_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                platform TEXT NOT NULL,
                username TEXT NOT NULL,
                display_name TEXT,
                status TEXT DEFAULT 'active',
                followers_count INTEGER DEFAULT 0,
                last_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS social_posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                platform TEXT NOT NULL,
                external_id TEXT UNIQUE,
                content TEXT NOT NULL,
                author_username TEXT,
                author_display_name TEXT,
                post_url TEXT,
                posted_at TIMESTAMP,
                sentiment TEXT,
                sentiment_score REAL,
                requires_response BOOLEAN DEFAULT TRUE,
                processed BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS responses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                social_post_id INTEGER,
                response_text TEXT NOT NULL,
                tone TEXT,
                confidence_score REAL,
                status TEXT DEFAULT 'pending',
                approved_by TEXT,
                approved_at TIMESTAMP,
                posted_to_platform BOOLEAN DEFAULT FALSE,
                platform_response_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (social_post_id) REFERENCES social_posts (id)
            )
        ''')
        
        # Add new columns to existing table if they don't exist
        try:
            cursor.execute('ALTER TABLE social_accounts ADD COLUMN monitor_keywords TEXT DEFAULT ""')
        except:
            pass
        try:
            cursor.execute('ALTER TABLE social_accounts ADD COLUMN monitor_hashtags TEXT DEFAULT ""')
        except:
            pass
        try:
            cursor.execute('ALTER TABLE social_accounts ADD COLUMN response_tone TEXT DEFAULT "friendly"')
        except:
            pass
        try:
            cursor.execute('ALTER TABLE social_accounts ADD COLUMN confidence_threshold REAL DEFAULT 0.8')
        except:
            pass
        try:
            cursor.execute('ALTER TABLE social_accounts ADD COLUMN monitor_mentions BOOLEAN DEFAULT 1')
        except:
            pass
        try:
            cursor.execute('ALTER TABLE social_accounts ADD COLUMN auto_generate BOOLEAN DEFAULT 1')
        except:
            pass
        try:
            cursor.execute('ALTER TABLE social_accounts ADD COLUMN realtime_monitoring BOOLEAN DEFAULT 1')
        except:
            pass
        try:
            cursor.execute('ALTER TABLE social_accounts ADD COLUMN time_window_hours INTEGER DEFAULT 2')
        except:
            pass

        conn.commit()
        conn.close()

        # Add your Twitter account if not exists
        self.add_twitter_account()
    
    def add_twitter_account(self):
        """Add your Twitter account to the database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Check if account exists
        cursor.execute("SELECT id FROM social_accounts WHERE platform = 'twitter' AND username = 'KarthikHar25823'")
        if not cursor.fetchone():
            cursor.execute('''
                INSERT INTO social_accounts (platform, username, display_name, status)
                VALUES ('twitter', 'KarthikHar25823', 'Karthik Hari', 'active')
            ''')
            conn.commit()
            print("✅ Added @KarthikHar25823 to social accounts")
        
        conn.close()

    def setup_phi3_service(self):
        """Setup Phi-3 Mini service for social media management"""
        if PHI3_AVAILABLE:
            try:
                self.phi3_service = Phi3AirlineService()
                # Note: In production, you would call await self.phi3_service.initialize()
                # For now, we'll initialize it when first needed
                print("✅ Phi-3 Mini service ready (will initialize on first use)")
            except Exception as e:
                print(f"❌ Phi-3 Mini setup failed: {e}")
                self.phi3_service = None
        else:
            print("⚠️ Phi-3 Mini not available - using fallback responses")
            self.phi3_service = None

    def setup_twitter_client(self):
        """Setup Twitter client using secure credentials"""
        try:
            # Try to get credentials from secure storage first
            if SECURE_CREDENTIALS_AVAILABLE:
                credentials = self.get_secure_twitter_credentials()
                if credentials:
                    self.twitter_client = tweepy.Client(
                        bearer_token=credentials.get('bearer_token'),
                        consumer_key=credentials.get('api_key'),
                        consumer_secret=credentials.get('api_secret'),
                        access_token=credentials.get('access_token'),
                        access_token_secret=credentials.get('access_token_secret'),
                        wait_on_rate_limit=True
                    )
                    print("✅ Twitter client ready (using secure credentials)")
                    return

            # Fallback to environment variables
            self.twitter_client = tweepy.Client(
                bearer_token=os.getenv('TWITTER_BEARER_TOKEN'),
                consumer_key=os.getenv('TWITTER_API_KEY'),
                consumer_secret=os.getenv('TWITTER_API_SECRET'),
                access_token=os.getenv('TWITTER_ACCESS_TOKEN'),
                access_token_secret=os.getenv('TWITTER_ACCESS_TOKEN_SECRET'),
                wait_on_rate_limit=True
            )
            print("✅ Twitter client ready (using environment variables)")

        except Exception as e:
            print(f"❌ Twitter client setup failed: {e}")
            self.twitter_client = None

    def get_secure_twitter_credentials(self):
        """Get Twitter credentials from secure storage"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT encrypted_credentials FROM secure_credentials
                WHERE platform = 'twitter' AND is_active = 1
                ORDER BY created_at DESC LIMIT 1
            ''')

            result = cursor.fetchone()
            conn.close()

            if result:
                credential_manager = get_credential_manager()
                return credential_manager.decrypt_credentials(result[0])

            return None

        except Exception as e:
            print(f"⚠️ Failed to get secure Twitter credentials: {e}")
            return None
    
    def store_mention(self, tweet_data, author_data):
        """Store a detected mention in the database (only if it doesn't already exist)"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # Check if tweet already exists
            cursor.execute("SELECT id FROM social_posts WHERE external_id = ?", (tweet_data['id'],))
            existing_post = cursor.fetchone()

            if existing_post:
                print(f"   ⚪ Tweet {tweet_data['id']} already exists in database, skipping")
                return existing_post[0]  # Return existing post ID

            # Analyze sentiment for new tweets only
            sentiment_result = self.analyze_sentiment(tweet_data['text'])

            # Extract media URLs if available
            media_urls = tweet_data.get('media_urls', [])
            import json
            media_urls_json = json.dumps(media_urls) if media_urls else None

            # Insert new tweet
            cursor.execute('''
                INSERT INTO social_posts
                (platform, external_id, content, author_username, author_display_name,
                 post_url, posted_at, sentiment, sentiment_score, requires_response, media_urls)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                'twitter',
                tweet_data['id'],
                tweet_data['text'],
                author_data.get('username', 'unknown'),
                author_data.get('name', 'Unknown'),
                f"https://twitter.com/{author_data.get('username', 'unknown')}/status/{tweet_data['id']}",
                tweet_data['created_at'],
                sentiment_result['sentiment'],
                sentiment_result['score'],
                True,
                media_urls_json
            ))

            post_id = cursor.lastrowid
            conn.commit()

            print(f"   ✅ Stored new tweet {tweet_data['id']} as post {post_id}")

            # Generate response for new tweets only
            self.generate_response(post_id, tweet_data, sentiment_result)

            return post_id

        except Exception as e:
            print(f"❌ Error storing mention: {e}")
            return None
        finally:
            conn.close()
    
    def analyze_sentiment(self, text):
        """Enhanced multilingual sentiment analysis with context awareness"""
        text_lower = text.lower()

        # Enhanced multilingual sentiment keywords
        negative_words = {
            # English
            'en': ['bad', 'terrible', 'awful', 'hate', 'broken', 'frustrated', 'problem', 'issue',
                   'delay', 'delayed', 'late', 'cancel', 'cancelled', 'angry', 'upset', 'disappointed',
                   'worst', 'horrible', 'unacceptable', 'disgusting', 'pathetic', 'useless'],

            # Tamil
            'ta': ['தாமதம்', 'தாமதமாக', 'ரத்து', 'கோபம்', 'வருத்தம்', 'மோசம்', 'கெட்ட', 'பிரச்சனை',
                   'சிக்கல்', 'நிகர்மம்', 'இழப்பீடு'],  # Added compensation-related terms

            # Hindi
            'hi': ['देरी', 'रद्द', 'गुस्सा', 'परेशानी', 'समस्या', 'बुरा', 'खराब'],

            # Spanish
            'es': ['retraso', 'cancelado', 'malo', 'terrible', 'problema', 'enojado'],

            # French
            'fr': ['retard', 'annulé', 'mauvais', 'terrible', 'problème', 'fâché']
        }

        positive_words = {
            'en': ['good', 'great', 'love', 'awesome', 'amazing', 'perfect', 'excellent', 'wonderful',
                   'fantastic', 'outstanding', 'brilliant', 'superb', 'thank', 'thanks'],
            'ta': ['நல்ல', 'சிறப்பு', 'அருமை', 'நன்றி', 'மகிழ்ச்சி'],
            'hi': ['अच्छा', 'बढ़िया', 'शानदार', 'धन्यवाद', 'खुशी'],
            'es': ['bueno', 'excelente', 'fantástico', 'gracias'],
            'fr': ['bon', 'excellent', 'fantastique', 'merci']
        }

        question_words = {
            'en': ['how', 'what', 'where', 'when', 'why', 'can', 'could', '?'],
            'ta': ['எப்படி', 'என்ன', 'எங்கே', 'எப்போது', 'ஏன்', '?'],
            'hi': ['कैसे', 'क्या', 'कहाँ', 'कब', 'क्यों', '?'],
            'es': ['cómo', 'qué', 'dónde', 'cuándo', 'por qué', '?'],
            'fr': ['comment', 'quoi', 'où', 'quand', 'pourquoi', '?']
        }

        # Context-specific indicators
        airline_complaint_indicators = {
            'en': ['flight', 'delay', 'compensation', 'refund', 'platinum', 'gold', 'member'],
            'ta': ['விமானம்', 'தாமதம்', 'நிகர்மம்', 'பிளாடினம்', 'உறுப்பினர்'],
            'hi': ['विमान', 'देरी', 'मुआवजा', 'प्लेटिनम'],
            'es': ['vuelo', 'retraso', 'compensación', 'platino'],
            'fr': ['vol', 'retard', 'compensation', 'platine']
        }

        # Count sentiment indicators across all languages
        negative_count = 0
        positive_count = 0
        question_count = 0
        complaint_context = 0

        for lang_code, words in negative_words.items():
            negative_count += sum(1 for word in words if word in text_lower or word in text)

        for lang_code, words in positive_words.items():
            positive_count += sum(1 for word in words if word in text_lower or word in text)

        for lang_code, words in question_words.items():
            question_count += sum(1 for word in words if word in text_lower or word in text)

        for lang_code, words in airline_complaint_indicators.items():
            complaint_context += sum(1 for word in words if word in text_lower or word in text)

        # Enhanced scoring with context awareness
        if complaint_context > 0 and negative_count > 0:
            # This is clearly an airline complaint
            score = -0.8 - (negative_count * 0.1)  # More negative for airline complaints
            return {"sentiment": "negative", "score": max(score, -1.0)}
        elif negative_count > positive_count:
            score = -0.6 - (negative_count * 0.05)
            return {"sentiment": "negative", "score": max(score, -1.0)}
        elif positive_count > negative_count:
            score = 0.7 + (positive_count * 0.05)
            return {"sentiment": "positive", "score": min(score, 1.0)}
        elif question_count > 0:
            return {"sentiment": "neutral_question", "score": 0.1}
        else:
            return {"sentiment": "neutral", "score": 0.0}
    
    def generate_response(self, post_id, tweet_data, sentiment_result):
        """Generate AI response for a post using Phi-3 Mini or fallback with image analysis"""
        text = tweet_data['text']
        sentiment = sentiment_result['sentiment']

        # Analyze images if present
        image_analysis = None
        media_urls = tweet_data.get('media_urls', [])
        if media_urls:
            print(f"🖼️ Analyzing {len(media_urls)} image(s) for enhanced response...")
            image_analysis = self._analyze_images(media_urls)

        # Try to use Phi-3 Mini for airline-specific responses
        if self.phi3_service and PHI3_AVAILABLE:
            try:
                # Create airline incident object with image context
                incident_type = self._classify_incident_type(text, image_analysis)

                incident = AirlineIncident(
                    text=text,
                    incident_type=incident_type,
                    sentiment=sentiment,
                    urgency=self._determine_urgency(text, sentiment_result),
                    customer_tier=self._get_customer_tier(tweet_data.get('author_username', 'unknown'))
                )

                # Generate response using Phi-3 Mini with image context
                phi3_response = self._generate_multimodal_response(incident, image_analysis)
                response = phi3_response['response_text']
                tone = "phi3_airline_optimized"
                confidence = phi3_response['confidence_score']

                print(f"✅ Generated Phi-3 Mini multimodal response for post {post_id}")

            except Exception as e:
                print(f"⚠️ Phi-3 Mini failed, using fallback: {e}")
                response, confidence, tone = self._generate_fallback_response(sentiment, image_analysis)
        else:
            # Use fallback response generation with image context
            response, confidence, tone = self._generate_fallback_response(sentiment, image_analysis)
        
        # Store response
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO responses
                (social_post_id, response_text, tone, confidence_score, status)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                post_id,
                response,
                tone,
                confidence,
                'pending'
            ))

            conn.commit()
            print(f"✅ Response generated for post {post_id}")

        except Exception as e:
            print(f"❌ Error generating response: {e}")
        finally:
            conn.close()

        # Return the generated response data for regeneration use
        return response, confidence, tone

    def _prepare_tweet_text(self, response_text, is_reply=True):
        """Prepare tweet text with proper character limits and uniqueness"""
        # Get character limit based on account type
        char_limit = self.twitter_config['pro_character_limit'] if self.twitter_config['is_pro_account'] else self.twitter_config['character_limit']

        # If response is too long, truncate intelligently
        if len(response_text) > char_limit:
            # Try to truncate at sentence boundary
            sentences = response_text.split('. ')
            truncated = ""
            for sentence in sentences:
                test_text = truncated + sentence + ". "
                if len(test_text) <= char_limit - 10:  # Leave room for uniqueness suffix
                    truncated = test_text
                else:
                    break

            if truncated:
                response_text = truncated.rstrip() + "..."
            else:
                # Fallback: hard truncate
                response_text = response_text[:char_limit-10] + "..."

        # Add uniqueness to avoid duplicate content detection
        if self.twitter_config['add_uniqueness'] and not is_reply:
            # For standalone tweets, add a subtle timestamp
            import time
            unique_suffix = f" [{int(time.time()) % 10000}]"
            if len(response_text) + len(unique_suffix) <= char_limit:
                response_text += unique_suffix

        return response_text

    def _check_rate_limits(self):
        """Check if we can post based on rate limiting rules"""
        if not self.twitter_config['rate_limit_protection']:
            return True, "Rate limiting disabled"

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Check last post time
        cursor.execute('''
            SELECT MAX(approved_at) FROM responses
            WHERE posted_to_platform = TRUE AND approved_at IS NOT NULL
        ''')

        last_post = cursor.fetchone()[0]
        if last_post:
            from datetime import datetime
            last_post_time = datetime.fromisoformat(last_post.replace('Z', '+00:00'))
            time_since_last = (datetime.now() - last_post_time.replace(tzinfo=None)).total_seconds()

            min_delay = self.twitter_config['min_delay_between_posts']
            if time_since_last < min_delay:
                remaining = min_delay - time_since_last
                return False, f"Rate limit: Wait {int(remaining)} seconds before next post"

        # Check posts in last hour
        cursor.execute('''
            SELECT COUNT(*) FROM responses
            WHERE posted_to_platform = TRUE
            AND approved_at > datetime('now', '-1 hour')
        ''')

        posts_last_hour = cursor.fetchone()[0]
        max_per_hour = self.twitter_config['max_posts_per_hour']

        if posts_last_hour >= max_per_hour:
            return False, f"Rate limit: Maximum {max_per_hour} posts per hour reached"

        conn.close()
        return True, "Rate limit check passed"

    def _add_human_like_delay(self):
        """Add random delay to make posting seem more human"""
        if self.twitter_config['human_like_delays']:
            import random
            # Random delay between 2-8 seconds
            delay = random.uniform(2, 8)
            print(f"⏱️ Adding human-like delay: {delay:.1f} seconds")
            time.sleep(delay)

    def _analyze_images(self, media_urls):
        """Enhanced image analysis to extract context for better response generation"""
        if not media_urls:
            return None

        analysis = {
            'has_images': True,
            'image_count': len(media_urls),
            'likely_content': [],
            'damage_indicators': False,
            'baggage_related': False,
            'airport_related': False,
            'flight_related': False,
            'document_related': False,
            'confidence_score': 0.0
        }

        print(f"🖼️ Analyzing {len(media_urls)} image(s) for context...")

        # Enhanced heuristic analysis based on URL patterns and context
        for i, url in enumerate(media_urls):
            print(f"📸 Analyzing image {i+1}: {url[:50]}...")

            # URL pattern analysis for content type hints
            url_lower = url.lower()

            # Check for common image hosting patterns that might indicate content type
            if any(pattern in url_lower for pattern in ['damage', 'broken', 'torn', 'crack']):
                analysis['damage_indicators'] = True
                analysis['likely_content'].append('damage_evidence')
                analysis['confidence_score'] += 0.3

            elif any(pattern in url_lower for pattern in ['bag', 'luggage', 'suitcase', 'baggage']):
                analysis['baggage_related'] = True
                analysis['likely_content'].append('baggage_issue')
                analysis['confidence_score'] += 0.25

            elif any(pattern in url_lower for pattern in ['airport', 'gate', 'terminal', 'departure', 'arrival']):
                analysis['airport_related'] = True
                analysis['likely_content'].append('airport_scene')
                analysis['confidence_score'] += 0.2

            elif any(pattern in url_lower for pattern in ['ticket', 'boarding', 'receipt', 'document']):
                analysis['document_related'] = True
                analysis['likely_content'].append('travel_document')
                analysis['confidence_score'] += 0.15

            elif any(pattern in url_lower for pattern in ['plane', 'aircraft', 'flight', 'wing']):
                analysis['flight_related'] = True
                analysis['likely_content'].append('aircraft_issue')
                analysis['confidence_score'] += 0.2
            else:
                # Generic customer evidence
                analysis['likely_content'].append('customer_evidence')
                analysis['confidence_score'] += 0.1

        # Normalize confidence score
        analysis['confidence_score'] = min(analysis['confidence_score'], 1.0)

        # Enhanced inference based on context combinations
        if analysis['baggage_related'] and analysis['damage_indicators']:
            analysis['likely_scenario'] = 'baggage_damage_claim'
            analysis['confidence_score'] += 0.2
        elif analysis['airport_related'] and analysis['flight_related']:
            analysis['likely_scenario'] = 'airport_flight_issue'
            analysis['confidence_score'] += 0.15
        elif analysis['document_related']:
            analysis['likely_scenario'] = 'documentation_issue'
            analysis['confidence_score'] += 0.1
        else:
            analysis['likely_scenario'] = 'general_complaint_with_evidence'

        # Cap confidence at 1.0
        analysis['confidence_score'] = min(analysis['confidence_score'], 1.0)

        print(f"📊 Enhanced image analysis: {analysis}")
        print(f"🎯 Likely scenario: {analysis.get('likely_scenario', 'unknown')} (confidence: {analysis['confidence_score']:.2f})")

        return analysis

    def _generate_multimodal_response(self, incident, image_analysis):
        """Generate response considering both text and image context without making compensation commitments"""
        if not image_analysis:
            return self.phi3_service._generate_fallback_response(incident)

        # Enhanced incident classification with image context - but avoid compensation promises
        if image_analysis.get('baggage_related') and image_analysis.get('damage_indicators'):
            # This is likely a baggage damage claim with visual evidence already provided
            response_text = (
                "Hi! I'm very sorry to see the damage to your baggage and the delay you experienced. "
                "This is absolutely not the service standard we aim for. Thank you for providing the photos - "
                "they help us understand the situation. Please DM us your booking reference so our team can "
                "review your case and explore available options according to our policies. We'll investigate this thoroughly! ✈️"
            )
            confidence = 0.90
        elif image_analysis.get('airport_related'):
            response_text = (
                "Thank you for sharing this with us. We can see the situation you're experiencing. "
                "Please DM us your booking details so our team can review your case and provide "
                "appropriate assistance based on our policies. 🛠️"
            )
            confidence = 0.85
        else:
            # General image-supported complaint
            response_text = (
                "Thank you for providing the visual details. This helps us understand your situation better. "
                "Please DM us your booking reference so we can investigate and explore how we can "
                "assist you according to our policies. We appreciate your patience! 💙"
            )
            confidence = 0.80

        return {
            'response_text': response_text,
            'confidence_score': confidence
        }

    def _classify_incident_type(self, text, image_analysis=None):
        """Intelligent incident classification using SML context understanding with image context"""

        # If we have image analysis indicating baggage damage, prioritize that
        if image_analysis and image_analysis.get('baggage_related') and image_analysis.get('damage_indicators'):
            print("🖼️ Image analysis indicates baggage damage - prioritizing baggage classification")
            return 'baggage'

        # Use Phi-3 Mini for intelligent classification instead of keyword matching
        if self.phi3_service and PHI3_AVAILABLE:
            try:
                return self._classify_with_phi3(text, image_analysis)
            except Exception as e:
                print(f"⚠️ Phi-3 classification failed, using fallback: {e}")
                return self._classify_with_context_analysis(text, image_analysis)
        else:
            return self._classify_with_context_analysis(text, image_analysis)

    def _classify_with_phi3(self, text, image_analysis=None):
        """Use Phi-3 Mini for intelligent incident classification with image context"""

        # Add image context to the prompt if available
        image_context = ""
        if image_analysis and image_analysis.get('has_images'):
            image_context = f"""

ADDITIONAL CONTEXT:
- Customer has provided {image_analysis['image_count']} image(s) as evidence
- Images likely show: {', '.join(image_analysis.get('likely_content', ['customer evidence']))}
- Damage indicators detected: {'Yes' if image_analysis.get('damage_indicators') else 'No'}
- Baggage-related visual content: {'Yes' if image_analysis.get('baggage_related') else 'No'}
- Airport-related visual content: {'Yes' if image_analysis.get('airport_related') else 'No'}

When images show damage or evidence, prioritize the appropriate category (e.g., baggage for damaged luggage).
"""

        classification_prompt = f"""<|system|>
You are an airline customer service expert. Classify the following customer message into ONE of these categories:

CATEGORIES:
- flight_delay: Flight delays, cancellations, schedule changes
- baggage: Lost, damaged, delayed baggage, luggage issues
- booking: Reservations, tickets, seat selection, check-in issues
- refund: Refunds, billing, charges, payment issues
- service_quality: Food, entertainment, crew behavior, comfort, cleanliness
- accessibility: Special assistance, wheelchair, medical needs
- airport_experience: Security, gates, lounges, ground services, aerobridge
- general_complaint: Other airline-related complaints
- not_airline: Not related to airline services

IMPORTANT:
- Consider context, not just keywords
- Recognize airline issues in ANY language
- Flight numbers (AA123, DL456) indicate airline context
- Customer status (platinum, gold) indicates airline context
- Airport/travel context indicates airline relevance
- Visual evidence (images) should influence classification{image_context}

<|user|>
Customer message: "{text}"

Respond with ONLY the category name, nothing else.

<|assistant|>
"""

        # Use Phi-3 for classification
        inputs = self.phi3_service.tokenizer(
            classification_prompt,
            return_tensors="pt",
            truncation=True,
            max_length=1024
        )

        import torch
        with torch.no_grad():
            outputs = self.phi3_service.model.generate(
                **inputs,
                max_new_tokens=20,
                temperature=0.1,
                do_sample=False,
                pad_token_id=self.phi3_service.tokenizer.eos_token_id
            )

        response = self.phi3_service.tokenizer.decode(
            outputs[0][inputs['input_ids'].shape[1]:],
            skip_special_tokens=True
        ).strip().lower()

        # Map response to valid categories
        valid_categories = [
            'flight_delay', 'baggage', 'booking', 'refund',
            'service_quality', 'accessibility', 'airport_experience',
            'general_complaint', 'not_airline'
        ]

        for category in valid_categories:
            if category in response:
                return category if category != 'not_airline' else 'general_complaint'

        # Default fallback
        return 'general_complaint'

    def _classify_with_context_analysis(self, text, image_analysis=None):
        """Enhanced fallback classification with comprehensive multi-language support and image context"""
        text_lower = text.lower()

        # If image analysis suggests baggage damage, prioritize baggage classification
        if image_analysis and image_analysis.get('baggage_related') and image_analysis.get('damage_indicators'):
            return 'baggage'

        # Flight number patterns (universal indicator)
        import re
        flight_pattern = r'\b[A-Z]{2}\d{2,4}\b'
        has_flight_number = bool(re.search(flight_pattern, text))

        # Multi-language airline terms
        airline_terms = {
            # English
            'flight': ['flight', 'plane', 'aircraft', 'airline'],
            'delay': ['delay', 'delayed', 'late', 'cancel', 'cancelled'],
            'baggage': ['bag', 'luggage', 'suitcase', 'lost', 'damaged', 'broken'],
            'airport': ['airport', 'gate', 'terminal', 'bridge', 'boarding'],
            'service': ['crew', 'staff', 'service', 'food', 'meal', 'seat', 'comfort'],
            'booking': ['book', 'ticket', 'reservation', 'check-in'],
            'refund': ['refund', 'money', 'charge', 'billing', 'payment'],
            'assistance': ['wheelchair', 'assistance', 'medical', 'special', 'disabled'],
            'status': ['platinum', 'gold', 'elite', 'frequent', 'miles', 'points'],

            # Tamil
            'flight_ta': ['விமானம்', 'விமான', 'வானூர்தி'],
            'delay_ta': ['தாமதம்', 'தாமதமாக', 'ரத்து'],
            'travel_ta': ['பயணம்', 'டிக்கெட்'],
            'status_ta': ['பிளாடினம்', 'கோல்ட்'],

            # Hindi
            'flight_hi': ['विमान', 'हवाई', 'उड़ान'],
            'delay_hi': ['देरी', 'रद्द', 'लेट'],
            'travel_hi': ['यात्रा', 'टिकट'],
            'status_hi': ['प्लेटिनम', 'गोल्ड'],

            # Spanish
            'flight_es': ['vuelo', 'avión', 'aerolínea'],
            'delay_es': ['retraso', 'cancelado', 'tarde'],
            'baggage_es': ['equipaje', 'maleta', 'perdido'],
            'travel_es': ['viaje', 'boleto'],

            # French
            'flight_fr': ['vol', 'avion', 'compagnie'],
            'delay_fr': ['retard', 'annulé', 'tard'],
            'baggage_fr': ['bagage', 'valise', 'perdu'],

            # German
            'flight_de': ['flug', 'flugzeug'],
            'delay_de': ['verspätung', 'storniert'],
            'baggage_de': ['gepäck', 'koffer']
        }

        # Check for airline context (any language)
        airline_context_found = False
        context_type = None

        # Flight numbers are universal
        if has_flight_number:
            airline_context_found = True
            context_type = 'flight'

        # Check all language terms with priority order
        for category, terms in airline_terms.items():
            if any(term in text_lower for term in terms) or any(term in text for term in terms):
                airline_context_found = True

                # Priority-based classification (most specific first)
                if ('delay' in category or 'cancel' in text_lower or 'रद्द' in text or 'ரத்து' in text or
                    'தாமதம்' in text or 'தாமதமாக' in text):
                    context_type = 'delay'
                    break  # High priority - break immediately
                elif ('assistance' in category or 'wheelchair' in text_lower or 'disabled' in text_lower or
                      'special' in text_lower):
                    context_type = 'assistance'
                    break  # High priority
                elif ('bridge' in text_lower or 'jet bridge' in text_lower or 'gate' in text_lower or
                      'terminal' in text_lower or 'airport' in category):
                    context_type = 'airport'
                elif ('baggage' in category or 'bag' in category or 'suitcase' in text_lower or
                      'equipaje' in text_lower or 'damaged' in text_lower or 'broken' in text_lower):
                    context_type = 'baggage'
                elif ('service' in category or 'food' in text_lower or 'meal' in text_lower or
                      'seat' in text_lower or 'comfort' in text_lower):
                    context_type = 'service'
                elif ('booking' in category or 'ticket' in text_lower or 'reservation' in text_lower or
                      'check-in' in text_lower):
                    context_type = 'booking'
                elif ('refund' in category or 'money' in text_lower or 'charge' in text_lower or
                      'billing' in text_lower):
                    context_type = 'refund'
                elif 'flight' in category:
                    context_type = 'flight'  # General flight context

        # If no airline context found, check if it's clearly non-airline
        if not airline_context_found:
            non_airline_indicators = ['website', 'online banking', 'internet', 'wifi', 'app', 'login', 'password']
            if any(term in text_lower for term in non_airline_indicators):
                return 'general_complaint'
            # If mentions airline handle but no airline context, still treat as airline-related
            elif '@' in text and any(term in text_lower for term in ['airline', 'airways', 'air']):
                return 'general_complaint'
            else:
                return 'general_complaint'

        # Classify based on detected context
        if context_type == 'delay':
            return 'flight_delay'
        elif context_type == 'baggage':
            return 'baggage'
        elif context_type == 'service':
            return 'service_quality'
        elif context_type == 'assistance':
            return 'accessibility'
        elif context_type == 'airport':
            return 'airport_experience'
        elif context_type == 'booking':
            return 'booking'
        elif context_type == 'refund':
            return 'refund'
        else:
            # Default for airline-related but unspecified
            return 'general_complaint'

    def _determine_urgency(self, text, sentiment_result):
        """Determine urgency level"""
        text_lower = text.lower()

        urgent_keywords = ['emergency', 'urgent', 'asap', 'immediately', 'stranded', 'stuck']
        if any(keyword in text_lower for keyword in urgent_keywords):
            return 'urgent'
        elif sentiment_result['sentiment'] == 'negative' and sentiment_result.get('score', 0) > 0.7:
            return 'high'
        elif sentiment_result['sentiment'] == 'negative':
            return 'medium'
        else:
            return 'low'

    def _get_customer_tier(self, username):
        """Determine customer tier (simplified)"""
        # In production, this would check a customer database
        if username and ('vip' in username.lower() or 'platinum' in username.lower()):
            return 'vip'
        elif username and ('frequent' in username.lower() or 'gold' in username.lower()):
            return 'frequent'
        else:
            return 'regular'

    def _generate_fallback_response(self, sentiment, image_analysis=None):
        """Generate fallback response when Phi-3 is not available, considering image context"""

        # If we have image analysis indicating damage/issues, customize response for airline context
        if image_analysis and image_analysis.get('damage_indicators'):
            if image_analysis.get('baggage_related'):
                response = (
                    "Hi! I can see the damage to your baggage in the image you've shared. "
                    "This is absolutely not acceptable, and I sincerely apologize. "
                    "Thank you for providing the photos - they help us understand the situation. "
                    "Please DM us your booking reference so our team can review your case and "
                    "explore available options according to our policies. ✈️"
                )
                tone = "apologetic_urgent"
                confidence = 0.85
            else:
                response = (
                    "Thank you for sharing the visual evidence of the issue you're experiencing. "
                    "I can see this is concerning, and we want to address it appropriately. "
                    "Please DM us your booking details so our team can review and assist. 🛠️"
                )
                tone = "professional_urgent"
                confidence = 0.80
        elif sentiment == "negative":
            response = f"Hi! I'm sorry to hear about your experience. I'd love to help resolve any issues you're facing. Could you share more details so I can assist you better? 💙"
            tone = "apologetic_helpful"
            confidence = 0.75
        elif sentiment == "positive":
            response = f"Thank you so much! 🙏 I'm thrilled you're interested in the AI social media handler. It's designed to help manage social media with AI assistance while keeping human oversight. Happy to answer any questions!"
            tone = "grateful_enthusiastic"
            confidence = 0.80
        elif sentiment == "neutral_question":
            response = f"Great question! 🤖 The AI handler monitors mentions, analyzes sentiment, generates responses, and queues them for human review before posting. It's designed for scalable social media management with privacy-focused local AI. What would you like to know more about?"
            tone = "informative_friendly"
            confidence = 0.85
        else:
            response = f"Hi! Thanks for reaching out! 👋 Is there anything specific about the AI social media handler you'd like to know? Happy to help!"
            tone = "friendly_open"
            confidence = 0.70

        return response, confidence, tone

    def generate_response_text(self, tweet_data, sentiment_result):
        """Generate AI response text only (for regeneration)"""
        sentiment = sentiment_result['sentiment']

        # Generate contextual response with more variety
        import random

        if sentiment == "negative":
            responses = [
                "Hi! I'm sorry to hear about your experience. I'd love to help resolve any issues you're facing. Could you share more details so I can assist you better? 💙",
                "Thank you for bringing this to our attention. We're looking into this and will get back to you shortly. Please DM us with more details! 🛠️",
                "We apologize for any inconvenience. Our team is working on a solution. Could you share more details via DM so we can assist you better? 🙏",
                "Sorry to hear about this issue! We take all feedback seriously. Please send us a DM with your details so we can help resolve this promptly. ⚡"
            ]
            tone = "apologetic_helpful"
        elif sentiment == "positive":
            responses = [
                "Thank you so much! 🙏 I'm thrilled you're interested in the AI social media handler. It's designed to help manage social media with AI assistance while keeping human oversight. Happy to answer any questions!",
                "We're so happy to hear this! 🎉 Thank you for the positive feedback. It really motivates our team to keep improving! 💪",
                "This made our day! 😊 Thank you for the wonderful feedback. We're committed to continuing to provide excellent service! 🌟",
                "Amazing to hear! 🚀 Your positive feedback energizes our entire team. Thank you for being such a valued part of our community! 🙌"
            ]
            tone = "grateful_enthusiastic"
        elif sentiment == "neutral_question":
            responses = [
                "Great question! 🤖 The AI handler monitors mentions, analyzes sentiment, generates responses, and queues them for human review before posting. It's designed for scalable social media management with privacy-focused local AI. What would you like to know more about?",
                "Thanks for reaching out! 👋 We're here to help. For the best assistance, please DM us with your specific question or check our FAQ section! 💡",
                "Hello! 👋 We appreciate you getting in touch. For personalized help, please send us a DM or visit our support page for immediate answers! 🚀",
                "Excellent question! 💭 Our team is here to provide you with the best answers. DM us for personalized assistance or browse our knowledge base! 📖"
            ]
            tone = "informative_friendly"
        else:
            responses = [
                "Hi! Thanks for reaching out! 👋 Is there anything specific about the AI social media handler you'd like to know? Happy to help!",
                "Hello! 👋 Thanks for getting in touch. How can we assist you today? Feel free to ask any questions! 😊",
                "Hi there! 🌟 We're here to help with any questions you might have. What can we do for you today?",
                "Thanks for reaching out! 💫 We'd love to help you with whatever you need. What's on your mind?"
            ]
            tone = "friendly_open"

        # Select random response for variety
        response_text = random.choice(responses)
        confidence_score = random.uniform(0.8, 0.95)

        return response_text, confidence_score, tone

    def generate_varied_response(self, tweet_data, sentiment_result, original_response):
        """Generate a context-aware varied response that addresses specific customer concerns"""
        import random

        sentiment = sentiment_result['sentiment']
        text = tweet_data['text']
        text_lower = text.lower()

        print(f"🎲 Generating varied response for sentiment: {sentiment}")
        print(f"📝 Analyzing context in: {text[:100]}...")

        # Analyze specific context and customer concerns
        context_analysis = self._analyze_customer_context(text)
        print(f"🔍 Context analysis: {context_analysis}")

        # Enhanced response variations based on sentiment and specific context
        if sentiment == "negative":
            # Context-specific responses for negative sentiment
            if context_analysis['has_flight_delay'] and context_analysis['has_compensation_request']:
                # Flight delay + compensation request (like the Tamil example)
                responses = [
                    f"Hi! We sincerely apologize for the {context_analysis.get('delay_duration', '4-hour')} delay on flight {context_analysis.get('flight_number', 'AA123')}. As a {context_analysis.get('customer_tier', 'Platinum')} member, you're entitled to compensation. Please DM us your booking reference so we can process your claim immediately. ✈️💙",
                    f"We're truly sorry about the significant delay with flight {context_analysis.get('flight_number', 'AA123')}. This is unacceptable service for our valued {context_analysis.get('customer_tier', 'Platinum')} members. Please DM your booking details - we'll prioritize your compensation and rebooking. 🙏",
                    f"I understand how frustrating a {context_analysis.get('delay_duration', '4-hour')} delay must be, especially as a {context_analysis.get('customer_tier', 'Platinum')} member. We take full responsibility and will ensure proper compensation. Please share your booking reference via DM for immediate assistance. 💙",
                    f"Absolutely unacceptable service - we sincerely apologize for the delay on {context_analysis.get('flight_number', 'AA123')}. As a {context_analysis.get('customer_tier', 'Platinum')} member, you deserve better. DM us your booking details and we'll expedite your compensation claim. ⚡",
                    f"We're deeply sorry for this {context_analysis.get('delay_duration', 'extended')} delay. Your {context_analysis.get('customer_tier', 'Platinum')} status means you're our priority for compensation and rebooking. Please DM your booking reference - our team will resolve this immediately. 🛠️"
                ]
                tone = "empathetic_compensation_focused"
            elif context_analysis['has_flight_delay']:
                # General flight delay responses
                responses = [
                    f"Hi! We sincerely apologize for the delay on flight {context_analysis.get('flight_number', 'your flight')}. Please DM us your booking reference for rebooking assistance and compensation information. ✈️",
                    f"We're truly sorry about the delay with flight {context_analysis.get('flight_number', 'your flight')}. Our team is here to help with rebooking and compensation. Please send us your details via DM. 🙏",
                    f"I understand how frustrating flight delays can be. Let us make this right - please DM your booking details so we can assist with rebooking and compensation for flight {context_analysis.get('flight_number', 'your flight')}. 💙",
                    f"We apologize for the inconvenience caused by the delay on {context_analysis.get('flight_number', 'your flight')}. Our priority is getting you to your destination and ensuring proper compensation. DM us your booking info! ⚡"
                ]
                tone = "empathetic_solution_focused"
            else:
                # General negative responses
                responses = [
                    "Hi! We sincerely apologize for the poor experience. Please DM us your booking reference so our team can investigate and make this right. ✈️",
                    "We're truly sorry to hear about this issue. Our team is here to help resolve this immediately. Please send us your details via DM. 🙏",
                    "I understand your frustration and we take full responsibility. Let us make this right - please DM your booking details for immediate assistance. 💙",
                    "We apologize for falling short of your expectations. Our priority is resolving this quickly - please share your booking reference via DM. ⚡"
                ]
                tone = "empathetic_solution_focused"
        elif sentiment == "positive":
            responses = [
                "Thank you so much for your positive feedback! 🙏 We're delighted to hear about your great experience. Your support means everything to our team! ✨",
                "This absolutely made our day! 😊 Thank you for taking the time to share such wonderful feedback. We're thrilled you had a great experience! 🌟",
                "We're so grateful for your kind words! 🎉 Feedback like yours motivates our entire team to keep delivering excellent service. Thank you! 💫",
                "Your positive review just brightened our whole team's day! 🚀 Thank you for being such a valued customer and for sharing your experience! 🙌",
                "Amazing to hear! 💙 Your satisfaction is our top priority, and knowing we exceeded your expectations is incredibly rewarding. Thank you! ⭐"
            ]
            tone = "grateful_enthusiastic"
        elif sentiment == "neutral_question":
            responses = [
                "Great question! 🤔 Our customer service team is here to help with any inquiries. Please DM us for personalized assistance or check our FAQ section! 💡",
                "Thanks for reaching out! 👋 We'd love to help answer your question. For the quickest response, please send us a DM with your specific inquiry! 🚀",
                "Hello! 💭 We appreciate you getting in touch. Our support team is ready to provide detailed answers - just DM us your question! 📖",
                "Excellent question! 🎯 For the most accurate and helpful response, please DM us with your specific inquiry. Our team is standing by! 💬",
                "Thanks for your inquiry! 🌟 We want to give you the best possible answer - please DM us with your question for personalized assistance! 🤝"
            ]
            tone = "helpful_informative"
        else:
            responses = [
                "Hello! 👋 Thanks for reaching out to us. How can we assist you today? We're here to help with any questions or concerns! 😊",
                "Hi there! 🌟 We appreciate you getting in touch. What can we do to help make your experience even better today? 💫",
                "Thanks for contacting us! 💙 Our team is ready to assist with whatever you need. How can we help you today? 🤝",
                "Hello! ✨ We're glad you reached out. Whether you have questions or need assistance, we're here to help! What's on your mind? 🎯",
                "Hi! 🚀 Thanks for connecting with us. We're committed to providing excellent service - how can we assist you today? 💪"
            ]
            tone = "friendly_professional"

        # Filter out responses that are too similar to the original
        filtered_responses = []
        for response in responses:
            # Simple similarity check - avoid responses with too many common words
            original_words = set(original_response.lower().split())
            new_words = set(response.lower().split())
            common_words = original_words.intersection(new_words)

            # If less than 40% of words are common, consider it different enough
            if len(common_words) / max(len(original_words), len(new_words)) < 0.4:
                filtered_responses.append(response)

        # If all responses are too similar, use the full list anyway but add variation
        if not filtered_responses:
            filtered_responses = responses
            print("⚠️ All responses too similar to original, using full list with variation")

        # Select a random response from filtered list
        response_text = random.choice(filtered_responses)

        # Add slight variation to confidence to simulate different model runs
        confidence_score = random.uniform(0.75, 0.92)

        print(f"🎲 Selected varied response (tone: {tone})")
        print(f"📝 New response preview: {response_text[:60]}...")

        return response_text, confidence_score, tone

    def _analyze_customer_context(self, text):
        """Analyze customer message for specific context and concerns"""
        import re

        context = {
            'has_flight_delay': False,
            'has_compensation_request': False,
            'has_baggage_issue': False,
            'has_booking_issue': False,
            'flight_number': None,
            'delay_duration': None,
            'customer_tier': None,
            'language': 'en'
        }

        text_lower = text.lower()

        # Detect language
        if any(char in text for char in ['த', 'ம', 'ன', 'ர', 'க', 'ல']):
            context['language'] = 'ta'  # Tamil
        elif any(char in text for char in ['ह', 'न', 'द', 'र', 'क', 'ल']):
            context['language'] = 'hi'  # Hindi

        # Extract flight number (universal pattern)
        flight_match = re.search(r'\b([A-Z]{2}\d{2,4})\b', text)
        if flight_match:
            context['flight_number'] = flight_match.group(1)
            context['has_flight_delay'] = True

        # Extract delay duration
        delay_patterns = [
            r'(\d+)\s*(?:hour|hr|மணி|घंटे)',
            r'(\d+)\s*(?:hours|hrs|மணி நேரம்|घंटों)'
        ]
        for pattern in delay_patterns:
            delay_match = re.search(pattern, text, re.IGNORECASE)
            if delay_match:
                hours = delay_match.group(1)
                context['delay_duration'] = f"{hours}-hour"
                context['has_flight_delay'] = True
                break

        # Detect customer tier (multilingual)
        tier_keywords = {
            'platinum': ['platinum', 'பிளாடினம்', 'प्लेटिनम'],
            'gold': ['gold', 'கோல்ட்', 'गोल्ड'],
            'elite': ['elite', 'எலைட்', 'एलीट'],
            'frequent': ['frequent', 'அடிக்கடி', 'बार-बार']
        }

        for tier, keywords in tier_keywords.items():
            if any(keyword in text_lower or keyword in text for keyword in keywords):
                context['customer_tier'] = tier.title()
                break

        # Detect specific issues
        delay_keywords = ['delay', 'delayed', 'late', 'தாமதம்', 'தாமதமாக', 'देरी', 'लेट']
        compensation_keywords = ['compensation', 'refund', 'நிகர்மம்', 'இழப்பீடு', 'मुआवजा', 'रिफंड']
        baggage_keywords = ['bag', 'luggage', 'suitcase', 'பை', 'சூட்கேஸ்', 'बैग', 'सामान']
        booking_keywords = ['booking', 'reservation', 'ticket', 'டிக்கெட்', 'बुकिंग', 'टिकट']

        context['has_flight_delay'] = context['has_flight_delay'] or any(
            keyword in text_lower or keyword in text for keyword in delay_keywords
        )
        context['has_compensation_request'] = any(
            keyword in text_lower or keyword in text for keyword in compensation_keywords
        )
        context['has_baggage_issue'] = any(
            keyword in text_lower or keyword in text for keyword in baggage_keywords
        )
        context['has_booking_issue'] = any(
            keyword in text_lower or keyword in text for keyword in booking_keywords
        )

        return context

    def _save_learning_data(self, context, user_feedback, response_effectiveness):
        """Save learning data for persistent model improvement"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # Create learning table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS response_learning (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    context_type TEXT,
                    customer_tier TEXT,
                    language TEXT,
                    user_feedback TEXT,
                    response_effectiveness REAL,
                    patterns_learned TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Save learning data
            patterns_learned = json.dumps({
                'flight_delay': context.get('has_flight_delay', False),
                'compensation_request': context.get('has_compensation_request', False),
                'customer_tier': context.get('customer_tier'),
                'language': context.get('language', 'en')
            })

            cursor.execute('''
                INSERT INTO response_learning
                (context_type, customer_tier, language, user_feedback, response_effectiveness, patterns_learned)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                'flight_delay_compensation' if context.get('has_compensation_request') else 'general',
                context.get('customer_tier', 'regular'),
                context.get('language', 'en'),
                user_feedback,
                response_effectiveness,
                patterns_learned
            ))

            conn.commit()
            print(f"💾 Saved learning data: {user_feedback} feedback for {context.get('language', 'en')} {context.get('customer_tier', 'regular')} customer")

        except Exception as e:
            print(f"❌ Error saving learning data: {e}")
        finally:
            conn.close()

    def _load_learning_patterns(self):
        """Load learned patterns for improved response generation"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT context_type, customer_tier, language,
                       AVG(response_effectiveness) as avg_effectiveness,
                       COUNT(*) as feedback_count,
                       patterns_learned
                FROM response_learning
                WHERE created_at > datetime('now', '-30 days')
                GROUP BY context_type, customer_tier, language
                HAVING feedback_count >= 2
                ORDER BY avg_effectiveness DESC
            ''')

            patterns = {}
            for row in cursor.fetchall():
                context_type, tier, lang, effectiveness, count, patterns_json = row
                key = f"{context_type}_{tier}_{lang}"
                patterns[key] = {
                    'effectiveness': effectiveness,
                    'feedback_count': count,
                    'patterns': json.loads(patterns_json) if patterns_json else {}
                }

            print(f"📚 Loaded {len(patterns)} learned patterns from last 30 days")
            return patterns

        except Exception as e:
            print(f"❌ Error loading learning patterns: {e}")
            return {}
        finally:
            conn.close()

# Initialize backend
backend = RealTimeBackend()

# API Endpoints
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Agentic ORM',
        'model': 'Phi-3 Mini',
        'privacy': 'Complete - Local Processing Only'
    })

@app.route('/api/v1/social-accounts/', methods=['GET'])
def get_social_accounts():
    """Get all social accounts"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, platform, username, display_name, status, followers_count, last_sync
        FROM social_accounts
    ''')
    
    accounts = []
    for row in cursor.fetchall():
        accounts.append({
            'id': row[0],
            'platform': row[1],
            'username': row[2],
            'display_name': row[3],
            'status': row[4],
            'followers_count': row[5],
            'last_sync': row[6]
        })
    
    conn.close()
    return jsonify({'accounts': accounts})

@app.route('/api/v1/responses/', methods=['GET'])
def get_responses():
    """Get responses with infinite scroll and search support"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    # Get query parameters
    limit = int(request.args.get('limit', 20))  # Default 20 items per load
    offset = int(request.args.get('offset', 0))  # For infinite scroll
    search = request.args.get('search', '').strip()  # Search query
    status_filter = request.args.get('status', '')  # Filter by status
    sentiment_filter = request.args.get('sentiment', '')  # Filter by sentiment

    # Build WHERE clause for search and filters
    where_conditions = []
    params = []

    if search:
        # Search across tweet content, author username, and response text
        where_conditions.append('''
            (p.content LIKE ? OR p.author_username LIKE ? OR r.response_text LIKE ?)
        ''')
        search_param = f'%{search}%'
        params.extend([search_param, search_param, search_param])

    if status_filter:
        where_conditions.append('r.status = ?')
        params.append(status_filter)

    if sentiment_filter:
        where_conditions.append('p.sentiment = ?')
        params.append(sentiment_filter)

    where_clause = ''
    if where_conditions:
        where_clause = 'WHERE ' + ' AND '.join(where_conditions)

    # Get total count for infinite scroll
    count_query = f'''
        SELECT COUNT(*)
        FROM responses r
        JOIN social_posts p ON r.social_post_id = p.id
        {where_clause}
    '''
    cursor.execute(count_query, params)
    total_count = cursor.fetchone()[0]

    # Get responses with pagination
    query = f'''
        SELECT r.id, r.response_text, r.tone, r.confidence_score, r.status,
               r.created_at, p.content, p.author_username, p.sentiment, p.external_id,
               p.created_at as post_created_at, p.post_url, p.media_urls
        FROM responses r
        JOIN social_posts p ON r.social_post_id = p.id
        {where_clause}
        ORDER BY r.created_at DESC
        LIMIT ? OFFSET ?
    '''

    cursor.execute(query, params + [limit, offset])

    responses = []
    for row in cursor.fetchall():
        # Parse media URLs from JSON
        media_urls = []
        if row[12]:  # media_urls column
            try:
                import json
                media_urls = json.loads(row[12])
            except:
                media_urls = []

        responses.append({
            'id': row[0],
            'response_text': row[1],
            'tone': row[2],
            'confidence_score': row[3],
            'status': row[4],
            'created_at': row[5],
            'original_post': {
                'content': row[6],
                'author_username': row[7],
                'sentiment': row[8],
                'external_id': row[9],
                'created_at': row[10],
                'post_url': row[11],
                'media_urls': media_urls
            }
        })

    conn.close()

    # Calculate if there are more items
    has_more = (offset + len(responses)) < total_count

    return jsonify({
        'responses': responses,
        'pagination': {
            'total': total_count,
            'limit': limit,
            'offset': offset,
            'has_more': has_more,
            'next_offset': offset + limit if has_more else None
        }
    })

@app.route('/api/v1/responses/<int:response_id>/approve', methods=['POST'])
def approve_response(response_id):
    """Approve a response and post to Twitter"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()
    
    # Get response data
    cursor.execute('''
        SELECT r.response_text, p.external_id, p.author_username
        FROM responses r
        JOIN social_posts p ON r.social_post_id = p.id
        WHERE r.id = ?
    ''', (response_id,))
    
    result = cursor.fetchone()
    if not result:
        return jsonify({'error': 'Response not found'}), 404
    
    response_text, original_tweet_id, author_username = result

    # Check rate limits before posting
    can_post, rate_limit_msg = backend._check_rate_limits()
    if not can_post:
        conn.close()
        return jsonify({
            'success': False,
            'error': 'Rate limit exceeded',
            'message': rate_limit_msg,
            'posted': False
        }), 429

    # Add human-like delay
    backend._add_human_like_delay()

    # Don't update status yet - only update after successful posting
    # Post to Twitter or simulate for test tweets
    posted = False
    tweet_url = None
    error_message = None

    # Check if this is a test tweet (starts with "test_")
    if original_tweet_id.startswith('test_'):
        # Simulate successful posting for test tweets
        posted = True
        simulated_tweet_id = f"sim_{int(time.time())}"
        tweet_url = f"https://twitter.com/KarthikHar25823/status/{simulated_tweet_id}"

        # Update response with simulated Twitter ID AND mark as approved
        cursor.execute('''
            UPDATE responses
            SET status = 'approved', approved_at = CURRENT_TIMESTAMP,
                posted_to_platform = TRUE, platform_response_id = ?
            WHERE id = ?
        ''', (simulated_tweet_id, response_id))
        conn.commit()

        print(f"✅ Simulated Twitter post for test tweet: {tweet_url}")

    elif backend.twitter_client:
        try:
            # Prepare tweet text with proper character limits
            prepared_text = backend._prepare_tweet_text(response_text, is_reply=True)

            # For demo purposes, if the tweet ID looks fake, post as standalone tweet
            if len(original_tweet_id) > 19 or not original_tweet_id.isdigit():
                # Post as standalone tweet with mention
                demo_text = backend._prepare_tweet_text(f"Demo response: {prepared_text}", is_reply=False)
                tweet = backend.twitter_client.create_tweet(text=demo_text)
                print(f"📝 Posted as standalone demo tweet (original tweet not found)")
                print(f"📏 Tweet length: {len(demo_text)} characters")
            else:
                # Try to post as reply to real tweet
                tweet = backend.twitter_client.create_tweet(
                    text=prepared_text,
                    in_reply_to_tweet_id=original_tweet_id
                )
                print(f"📝 Posted as reply to tweet {original_tweet_id}")
                print(f"📏 Tweet length: {len(prepared_text)} characters")

            if tweet.data:
                posted = True
                tweet_url = f"https://twitter.com/KarthikHar25823/status/{tweet.data['id']}"

                # Update with posted info AND mark as approved
                cursor.execute('''
                    UPDATE responses
                    SET status = 'approved', approved_at = CURRENT_TIMESTAMP,
                        posted_to_platform = TRUE, platform_response_id = ?
                    WHERE id = ?
                ''', (tweet.data['id'], response_id))
                conn.commit()

                print(f"✅ Posted response to Twitter: {tweet_url}")

        except Exception as e:
            error_message = f"Twitter API error: {str(e)}"
            print(f"❌ Error posting to Twitter: {e}")

            # Check if it's a 403 Forbidden error
            if "403" in str(e) or "Forbidden" in str(e) or "not permitted" in str(e).lower():
                print(f"🔒 403 Forbidden - This is likely due to:")
                print(f"   • App permissions (need write access)")
                print(f"   • Tweet visibility (original tweet not accessible)")
                print(f"   • Account restrictions or rate limits")
                print(f"   • Tweet ID {original_tweet_id} may not exist or be private")

                # For 403 errors, try a standalone demo tweet as fallback
                try:
                    demo_text = f"Demo: Customer service response generated by AI system. Original context: baggage damage + flight delay. Response: {response_text}"
                    prepared_demo = backend._prepare_tweet_text(demo_text, is_reply=False)

                    tweet = backend.twitter_client.create_tweet(text=prepared_demo)
                    print(f"📏 Demo tweet length: {len(prepared_demo)} characters")

                    if tweet.data:
                        posted = True
                        tweet_url = f"https://twitter.com/KarthikHar25823/status/{tweet.data['id']}"
                        error_message = f"Original reply failed (403), posted as demo tweet instead"

                        cursor.execute('''
                            UPDATE responses
                            SET status = 'approved', approved_at = CURRENT_TIMESTAMP,
                                posted_to_platform = TRUE, platform_response_id = ?
                            WHERE id = ?
                        ''', (tweet.data['id'], response_id))
                        conn.commit()

                        print(f"✅ Posted as demo tweet due to 403 error: {tweet_url}")

                except Exception as e2:
                    error_message = f"Twitter API error: Original 403 Forbidden, demo tweet also failed: {str(e2)}"
                    print(f"❌ Demo tweet also failed: {e2}")

            # For other errors, try the original fallback
            elif "reply" in str(e).lower() or "not visible" in str(e).lower():
                try:
                    demo_text = f"AI Response Demo: {response_text} #AIAutomation #SocialMediaAI"
                    prepared_demo = backend._prepare_tweet_text(demo_text, is_reply=False)
                    tweet = backend.twitter_client.create_tweet(text=prepared_demo)
                    print(f"📏 Fallback tweet length: {len(prepared_demo)} characters")

                    if tweet.data:
                        posted = True
                        tweet_url = f"https://twitter.com/KarthikHar25823/status/{tweet.data['id']}"
                        error_message = None

                        cursor.execute('''
                            UPDATE responses
                            SET status = 'approved', approved_at = CURRENT_TIMESTAMP,
                                posted_to_platform = TRUE, platform_response_id = ?
                            WHERE id = ?
                        ''', (tweet.data['id'], response_id))
                        conn.commit()

                        print(f"✅ Posted as standalone demo tweet: {tweet_url}")

                except Exception as e2:
                    error_message = f"Twitter API error (both reply and standalone failed): {str(e2)}"
                    print(f"❌ Both reply and standalone posting failed: {e2}")
    else:
        error_message = "Twitter client not configured"

    conn.close()

    return jsonify({
        'success': True,
        'posted': posted,
        'tweet_url': tweet_url,
        'error_message': error_message,
        'is_simulation': original_tweet_id.startswith('test_')
    })

@app.route('/api/v1/responses/<int:response_id>/reject', methods=['POST'])
def reject_response(response_id):
    """Reject a response"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    # Update response status
    cursor.execute('''
        UPDATE responses
        SET status = 'rejected', approved_at = CURRENT_TIMESTAMP
        WHERE id = ? AND status = 'pending'
    ''', (response_id,))

    if cursor.rowcount == 0:
        conn.close()
        return jsonify({'error': 'Response not found or already processed'}), 404

    conn.commit()
    conn.close()

    return jsonify({'success': True})

@app.route('/api/v1/responses/<int:response_id>/edit', methods=['PUT'])
def edit_response(response_id):
    """Edit a response text"""
    data = request.json
    new_text = data.get('response_text', '').strip()

    if not new_text:
        return jsonify({'error': 'Response text is required'}), 400

    if len(new_text) > 280:
        return jsonify({'error': 'Response text too long (max 280 characters)'}), 400

    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    # Update response text
    cursor.execute('''
        UPDATE responses
        SET response_text = ?
        WHERE id = ? AND status = 'pending'
    ''', (new_text, response_id))

    if cursor.rowcount == 0:
        conn.close()
        return jsonify({'error': 'Response not found or already processed'}), 404

    conn.commit()
    conn.close()

    return jsonify({'success': True})

@app.route('/api/v1/responses/<int:response_id>/regenerate', methods=['POST'])
def regenerate_response(response_id):
    """Regenerate a response with variation and create a new pending response for approval"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    # Get original response and post details
    cursor.execute('''
        SELECT r.social_post_id, r.response_text as original_response,
               sp.content, sp.external_id, sp.author_username, sp.media_urls
        FROM responses r
        JOIN social_posts sp ON r.social_post_id = sp.id
        WHERE r.id = ? AND r.status = 'pending'
    ''', (response_id,))

    result = cursor.fetchone()
    if not result:
        conn.close()
        return jsonify({'error': 'Response not found or already processed'}), 404

    post_id, original_response, content, external_id, author_username, media_urls_json = result

    print(f"🔄 Regenerating response for: {content[:100]}...")
    print(f"📝 Original response: {original_response[:50]}...")

    # Parse media URLs
    media_urls = []
    if media_urls_json:
        try:
            import json
            media_urls = json.loads(media_urls_json)
            print(f"🖼️ Found {len(media_urls)} media URLs for regeneration")
        except:
            media_urls = []

    # Create tweet data for regeneration
    tweet_data = {
        'id': external_id,
        'text': content,
        'created_at': datetime.now().isoformat() + 'Z',
        'media_urls': media_urls
    }

    try:
        # Analyze sentiment first
        sentiment_result = backend.analyze_sentiment(content)
        print(f"📊 Sentiment analysis: {sentiment_result}")

        # Generate a varied response using enhanced generation
        new_response_text, confidence, tone = backend.generate_varied_response(
            tweet_data, sentiment_result, original_response
        )

        print(f"✅ Generated new response: {new_response_text[:100]}...")
        print(f"   Confidence: {confidence}, Tone: {tone}")

        # Update the existing response with the new content
        cursor.execute('''
            UPDATE responses
            SET response_text = ?, confidence_score = ?, tone = ?,
                regenerated_at = CURRENT_TIMESTAMP, regeneration_count = COALESCE(regeneration_count, 0) + 1
            WHERE id = ?
        ''', (new_response_text, confidence, tone, response_id))

        conn.commit()
        conn.close()

        print(f"✅ Regenerated response for post {post_id}")

        return jsonify({
            'success': True,
            'new_response': new_response_text,
            'original_response': original_response,
            'requires_approval': True
        })

    except Exception as e:
        print(f"❌ Error regenerating response: {e}")
        conn.close()
        return jsonify({'error': f'Failed to regenerate response: {str(e)}'}), 500

@app.route('/api/v1/responses/<int:response_id>/feedback', methods=['POST'])
def log_response_feedback(response_id):
    """Enhanced feedback logging with context analysis for model learning"""
    data = request.json
    action = data.get('action')  # 'accept_regeneration' or 'reject_regeneration'
    feedback_type = data.get('feedback_type')  # 'positive' or 'negative'

    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    try:
        # Get response and post context for learning
        cursor.execute('''
            SELECT r.response_text, sp.content, sp.author_username
            FROM responses r
            JOIN social_posts sp ON r.social_post_id = sp.id
            WHERE r.id = ?
        ''', (response_id,))

        result = cursor.fetchone()
        if not result:
            return jsonify({'error': 'Response not found'}), 404

        response_text, post_content, author_username = result

        # Analyze context for learning
        context = backend._analyze_customer_context(post_content)

        # Create feedback table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS response_feedback (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                response_id INTEGER,
                action TEXT,
                feedback_type TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (response_id) REFERENCES responses (id)
            )
        ''')

        # Insert feedback
        cursor.execute('''
            INSERT INTO response_feedback (response_id, action, feedback_type)
            VALUES (?, ?, ?)
        ''', (response_id, action, feedback_type))

        # Save learning data for model improvement
        response_effectiveness = 1.0 if feedback_type == 'positive' else 0.0
        backend._save_learning_data(context, action, response_effectiveness)

        conn.commit()

        print(f"📊 Logged feedback: {action} ({feedback_type}) for response {response_id}")
        print(f"🧠 Context learned: {context.get('language', 'en')} {context.get('customer_tier', 'regular')} customer, flight_delay={context.get('has_flight_delay', False)}")

        return jsonify({
            'success': True,
            'message': 'Feedback logged for model improvement',
            'context_learned': {
                'language': context.get('language', 'en'),
                'customer_tier': context.get('customer_tier', 'regular'),
                'has_flight_delay': context.get('has_flight_delay', False),
                'has_compensation_request': context.get('has_compensation_request', False)
            }
        })

    except Exception as e:
        print(f"❌ Error logging feedback: {e}")
        return jsonify({'error': f'Failed to log feedback: {str(e)}'}), 500
    finally:
        conn.close()

@app.route('/api/v1/learning/patterns', methods=['GET'])
def get_learning_patterns():
    """Get current learning patterns and rules"""
    try:
        # Load current learning patterns
        patterns = backend._load_learning_patterns()

        # Get recent feedback stats
        conn = sqlite3.connect(backend.db_path)
        cursor = conn.cursor()

        # Get feedback summary
        cursor.execute('''
            SELECT
                action,
                feedback_type,
                COUNT(*) as count,
                AVG(CASE WHEN feedback_type = 'positive' THEN 1.0 ELSE 0.0 END) as success_rate
            FROM response_feedback
            WHERE created_at > datetime('now', '-7 days')
            GROUP BY action, feedback_type
            ORDER BY count DESC
        ''')
        feedback_stats = cursor.fetchall()

        # Get language distribution
        cursor.execute('''
            SELECT
                rl.language,
                COUNT(*) as feedback_count,
                AVG(rl.response_effectiveness) as avg_effectiveness
            FROM response_learning rl
            WHERE rl.created_at > datetime('now', '-30 days')
            GROUP BY rl.language
            ORDER BY feedback_count DESC
        ''')
        language_stats = cursor.fetchall()

        # Get context type performance
        cursor.execute('''
            SELECT
                rl.context_type,
                rl.customer_tier,
                COUNT(*) as count,
                AVG(rl.response_effectiveness) as effectiveness
            FROM response_learning rl
            WHERE rl.created_at > datetime('now', '-30 days')
            GROUP BY rl.context_type, rl.customer_tier
            ORDER BY effectiveness DESC
        ''')
        context_stats = cursor.fetchall()

        conn.close()

        return jsonify({
            'success': True,
            'learning_patterns': patterns,
            'feedback_stats': [
                {
                    'action': row[0],
                    'feedback_type': row[1],
                    'count': row[2],
                    'success_rate': row[3]
                } for row in feedback_stats
            ],
            'language_stats': [
                {
                    'language': row[0],
                    'feedback_count': row[1],
                    'avg_effectiveness': row[2]
                } for row in language_stats
            ],
            'context_stats': [
                {
                    'context_type': row[0],
                    'customer_tier': row[1],
                    'count': row[2],
                    'effectiveness': row[3]
                } for row in context_stats
            ],
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ Error getting learning patterns: {e}")
        return jsonify({'error': f'Failed to get learning patterns: {str(e)}'}), 500

@app.route('/api/v1/learning/rules', methods=['GET'])
def get_custom_rules():
    """Get custom rules defined by users"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    try:
        # Create custom rules table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS custom_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rule_name TEXT NOT NULL,
                rule_description TEXT,
                rule_condition TEXT,
                rule_action TEXT,
                priority INTEGER DEFAULT 1,
                active BOOLEAN DEFAULT TRUE,
                created_by TEXT DEFAULT 'admin',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        cursor.execute('''
            SELECT id, rule_name, rule_description, rule_condition, rule_action,
                   priority, active, created_by, created_at, updated_at
            FROM custom_rules
            ORDER BY priority DESC, created_at DESC
        ''')

        rules = []
        for row in cursor.fetchall():
            rules.append({
                'id': row[0],
                'rule_name': row[1],
                'rule_description': row[2],
                'rule_condition': row[3],
                'rule_action': row[4],
                'priority': row[5],
                'active': bool(row[6]),
                'created_by': row[7],
                'created_at': row[8],
                'updated_at': row[9]
            })

        return jsonify({'success': True, 'rules': rules})

    except Exception as e:
        print(f"❌ Error getting custom rules: {e}")
        return jsonify({'error': f'Failed to get custom rules: {str(e)}'}), 500
    finally:
        conn.close()

@app.route('/api/v1/learning/rules', methods=['POST'])
def create_custom_rule():
    """Create a new custom rule"""
    data = request.json

    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    try:
        cursor.execute('''
            INSERT INTO custom_rules
            (rule_name, rule_description, rule_condition, rule_action, priority, created_by)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            data.get('rule_name'),
            data.get('rule_description'),
            data.get('rule_condition'),
            data.get('rule_action'),
            data.get('priority', 1),
            data.get('created_by', 'admin')
        ))

        conn.commit()
        rule_id = cursor.lastrowid

        print(f"📋 Created custom rule: {data.get('rule_name')}")

        return jsonify({
            'success': True,
            'rule_id': rule_id,
            'message': 'Custom rule created successfully'
        })

    except Exception as e:
        print(f"❌ Error creating custom rule: {e}")
        return jsonify({'error': f'Failed to create custom rule: {str(e)}'}), 500
    finally:
        conn.close()

@app.route('/api/v1/reporting/user-insights', methods=['GET'])
def get_user_insights():
    """Get comprehensive user insights for business intelligence"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    try:
        # Get user sentiment analysis
        cursor.execute('''
            SELECT
                sp.author_username,
                sp.author_display_name,
                COUNT(*) as total_posts,
                AVG(CASE
                    WHEN sp.sentiment = 'positive' THEN 1.0
                    WHEN sp.sentiment = 'negative' THEN -1.0
                    ELSE 0.0
                END) as avg_sentiment_score,
                COUNT(CASE WHEN sp.sentiment = 'positive' THEN 1 END) as positive_posts,
                COUNT(CASE WHEN sp.sentiment = 'negative' THEN 1 END) as negative_posts,
                COUNT(CASE WHEN sp.sentiment = 'neutral' THEN 1 END) as neutral_posts,
                sp.language,
                sp.scenario,
                MAX(sp.created_at) as last_interaction,
                COUNT(r.id) as responses_received
            FROM social_posts sp
            LEFT JOIN responses r ON sp.id = r.social_post_id
            WHERE sp.created_at > datetime('now', '-30 days')
            GROUP BY sp.author_username, sp.language, sp.scenario
            HAVING total_posts >= 1
            ORDER BY total_posts DESC, avg_sentiment_score ASC
        ''')

        user_insights = []
        for row in cursor.fetchall():
            username, display_name, total_posts, avg_sentiment, positive, negative, neutral, language, scenario, last_interaction, responses = row

            # Calculate sentiment category
            if avg_sentiment > 0.3:
                sentiment_category = "positive"
            elif avg_sentiment < -0.3:
                sentiment_category = "negative"
            else:
                sentiment_category = "neutral"

            # Calculate engagement level
            if total_posts >= 5:
                engagement_level = "high"
            elif total_posts >= 2:
                engagement_level = "medium"
            else:
                engagement_level = "low"

            user_insights.append({
                'username': username,
                'display_name': display_name,
                'total_posts': total_posts,
                'avg_sentiment_score': round(avg_sentiment, 2),
                'sentiment_category': sentiment_category,
                'engagement_level': engagement_level,
                'positive_posts': positive,
                'negative_posts': negative,
                'neutral_posts': neutral,
                'language': language,
                'scenario': scenario,
                'last_interaction': last_interaction,
                'responses_received': responses,
                'response_rate': round((responses / total_posts * 100), 1) if total_posts > 0 else 0
            })

        # Get trending issues
        cursor.execute('''
            SELECT
                sp.scenario,
                sp.language,
                COUNT(*) as frequency,
                AVG(CASE
                    WHEN sp.sentiment = 'positive' THEN 1.0
                    WHEN sp.sentiment = 'negative' THEN -1.0
                    ELSE 0.0
                END) as avg_sentiment,
                COUNT(CASE WHEN sp.sentiment = 'negative' THEN 1 END) as negative_count
            FROM social_posts sp
            WHERE sp.created_at > datetime('now', '-7 days')
            AND sp.scenario IS NOT NULL
            GROUP BY sp.scenario, sp.language
            ORDER BY frequency DESC, negative_count DESC
            LIMIT 10
        ''')

        trending_issues = []
        for row in cursor.fetchall():
            scenario, language, frequency, avg_sentiment, negative_count = row
            trending_issues.append({
                'scenario': scenario,
                'language': language,
                'frequency': frequency,
                'avg_sentiment': round(avg_sentiment, 2),
                'negative_count': negative_count,
                'urgency_score': round((negative_count / frequency * 100), 1) if frequency > 0 else 0
            })

        # Get language distribution
        cursor.execute('''
            SELECT
                language,
                COUNT(*) as post_count,
                AVG(CASE
                    WHEN sentiment = 'positive' THEN 1.0
                    WHEN sentiment = 'negative' THEN -1.0
                    ELSE 0.0
                END) as avg_sentiment
            FROM social_posts
            WHERE created_at > datetime('now', '-30 days')
            GROUP BY language
            ORDER BY post_count DESC
        ''')

        language_insights = []
        for row in cursor.fetchall():
            language, post_count, avg_sentiment = row
            language_insights.append({
                'language': language,
                'post_count': post_count,
                'avg_sentiment': round(avg_sentiment, 2)
            })

        return jsonify({
            'success': True,
            'user_insights': user_insights,
            'trending_issues': trending_issues,
            'language_insights': language_insights,
            'summary': {
                'total_users': len(user_insights),
                'high_engagement_users': len([u for u in user_insights if u['engagement_level'] == 'high']),
                'negative_sentiment_users': len([u for u in user_insights if u['sentiment_category'] == 'negative']),
                'multilingual_users': len([u for u in user_insights if u['language'] != 'en'])
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ Error getting user insights: {e}")
        return jsonify({'error': f'Failed to get user insights: {str(e)}'}), 500
    finally:
        conn.close()

@app.route('/api/v1/dashboard/stats', methods=['GET'])
def get_dashboard_stats():
    """Get dashboard statistics"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()
    
    # Get counts
    cursor.execute("SELECT COUNT(*) FROM social_posts WHERE created_at > datetime('now', '-7 days')")
    posts_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM responses WHERE status = 'pending'")
    pending_responses = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM responses WHERE posted_to_platform = TRUE")
    posted_responses = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM social_accounts WHERE status = 'active'")
    active_accounts = cursor.fetchone()[0]
    
    conn.close()
    
    return jsonify({
        'posts_detected': posts_count,
        'pending_responses': pending_responses,
        'posted_responses': posted_responses,
        'active_accounts': active_accounts,
        'last_updated': datetime.now().isoformat()
    })

@app.route('/api/v1/analytics/sentiment', methods=['GET'])
def get_sentiment_analytics():
    """Get sentiment analysis data"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    # Get sentiment distribution from actual stored sentiment data
    cursor.execute('''
        SELECT
            COALESCE(sentiment, 'neutral') as sentiment,
            COUNT(*) as count
        FROM social_posts
        GROUP BY sentiment
    ''')

    sentiment_data = cursor.fetchall()

    # Calculate percentages
    total_posts = sum(count for _, count in sentiment_data)
    sentiment_stats = {}

    for sentiment, count in sentiment_data:
        percentage = (count / total_posts * 100) if total_posts > 0 else 0
        sentiment_stats[sentiment] = {
            'count': count,
            'percentage': round(percentage, 1)
        }

    # Ensure all sentiments are present
    for sentiment in ['positive', 'negative', 'neutral']:
        if sentiment not in sentiment_stats:
            sentiment_stats[sentiment] = {'count': 0, 'percentage': 0}

    conn.close()

    return jsonify({
        'sentiment_distribution': sentiment_stats,
        'total_posts': total_posts
    })

@app.route('/api/v1/analytics/platform', methods=['GET'])
def get_platform_analytics():
    """Get platform performance data"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    # Get platform distribution
    cursor.execute('''
        SELECT platform, COUNT(*) as post_count
        FROM social_posts
        GROUP BY platform
    ''')

    platform_data = cursor.fetchall()

    # Get response counts by platform
    cursor.execute('''
        SELECT sp.platform, COUNT(r.id) as response_count
        FROM social_posts sp
        LEFT JOIN responses r ON sp.id = r.social_post_id
        WHERE r.status = 'approved'
        GROUP BY sp.platform
    ''')

    response_data = cursor.fetchall()
    response_dict = dict(response_data)

    platform_stats = {}
    for platform, post_count in platform_data:
        platform_stats[platform] = {
            'posts': post_count,
            'responses': response_dict.get(platform, 0)
        }

    conn.close()

    return jsonify({
        'platform_performance': platform_stats
    })

@app.route('/api/v1/analytics/timeline', methods=['GET'])
def get_timeline_analytics():
    """Get timeline data for charts"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    # Get posts by day for last 7 days
    cursor.execute('''
        SELECT
            DATE(created_at) as date,
            COUNT(*) as posts,
            COUNT(CASE WHEN content LIKE '%love%' OR content LIKE '%great%' THEN 1 END) as positive,
            COUNT(CASE WHEN content LIKE '%hate%' OR content LIKE '%bad%' THEN 1 END) as negative
        FROM social_posts
        WHERE created_at >= DATE('now', '-7 days')
        GROUP BY DATE(created_at)
        ORDER BY date
    ''')

    timeline_data = cursor.fetchall()

    conn.close()

    return jsonify({
        'timeline': [
            {
                'date': date,
                'posts': posts,
                'positive': positive,
                'negative': negative,
                'neutral': posts - positive - negative
            }
            for date, posts, positive, negative in timeline_data
        ]
    })

# Posts endpoints
@app.route('/api/v1/posts/', methods=['POST'])
def create_post():
    """Create a new social media post"""
    data = request.json

    tweet_data = {
        'id': data['external_id'],
        'text': data['content'],
        'created_at': data['posted_at']
    }

    author_data = {
        'username': data['author_username'],
        'name': data['author_display_name']
    }

    post_id = backend.store_mention(tweet_data, author_data)

    if post_id:
        return jsonify({'success': True, 'post_id': post_id}), 201
    else:
        return jsonify({'success': False}), 500

@app.route('/api/v1/posts/check/<external_id>', methods=['GET'])
def check_post_exists(external_id):
    """Check if a post already exists"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    cursor.execute("SELECT id FROM social_posts WHERE external_id = ?", (external_id,))
    result = cursor.fetchone()

    conn.close()

    if result:
        return jsonify({'exists': True, 'post_id': result[0]})
    else:
        return jsonify({'exists': False}), 404

# Add a test mention for demo
@app.route('/api/v1/test/add-mention', methods=['POST'])
def add_test_mention():
    """Add a test mention for demo purposes"""
    test_tweet = {
        'id': f"test_{int(time.time())}",
        'text': "Hey @KarthikHar25823, I'm testing your AI social media handler! How does it work? 🤖",
        'created_at': datetime.now().isoformat() + 'Z'
    }

    test_author = {
        'username': 'test_user',
        'name': 'Test User'
    }

    post_id = backend.store_mention(test_tweet, test_author)

    if post_id:
        return jsonify({'success': True, 'post_id': post_id})
    else:
        return jsonify({'success': False}), 500

@app.route('/api/v1/test/add-real-mention', methods=['POST'])
def add_real_mention():
    """Add a mention with a real Twitter ID for testing actual posting"""
    data = request.json

    # Use a real tweet ID if provided, otherwise use a realistic format
    real_tweet_id = data.get('tweet_id', '1733248123456789012')  # Realistic Twitter ID format

    real_tweet = {
        'id': real_tweet_id,
        'text': data.get('text', "Hey @KarthikHar25823, I love your AI social media handler! Can you tell me more about how it works? 🚀"),
        'created_at': datetime.now().isoformat() + 'Z'
    }

    real_author = {
        'username': data.get('username', 'real_user'),
        'name': data.get('name', 'Real User')
    }

    post_id = backend.store_mention(real_tweet, real_author)

    if post_id:
        return jsonify({
            'success': True,
            'post_id': post_id,
            'tweet_id': real_tweet_id,
            'message': 'Real mention added - this can be approved and posted to Twitter'
        })
    else:
        return jsonify({'success': False}), 500

@app.route('/api/v1/test/simulate-approval', methods=['POST'])
def simulate_approval():
    """Simulate a successful approval without actually posting to Twitter"""
    data = request.json
    response_id = data.get('response_id')

    if not response_id:
        return jsonify({'error': 'response_id required'}), 400

    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    # Update response status to approved
    cursor.execute('''
        UPDATE responses
        SET status = 'approved',
            approved_at = CURRENT_TIMESTAMP,
            platform_response_id = ?
        WHERE id = ? AND status = 'pending'
    ''', (f"simulated_{int(time.time())}", response_id))

    if cursor.rowcount == 0:
        conn.close()
        return jsonify({'error': 'Response not found or already processed'}), 404

    conn.commit()
    conn.close()

    return jsonify({
        'success': True,
        'posted': True,
        'tweet_url': f"https://twitter.com/KarthikHar25823/status/simulated_{int(time.time())}",
        'message': 'Response approved and simulated as posted'
    })

@app.route('/api/v1/social-accounts/<username>/settings', methods=['GET'])
def get_account_settings(username):
    """Get account settings"""
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    cursor.execute('''
        SELECT monitor_keywords, monitor_hashtags, response_tone, confidence_threshold,
               monitor_mentions, auto_generate, realtime_monitoring, time_window_hours
        FROM social_accounts
        WHERE username = ?
    ''', (username,))

    result = cursor.fetchone()
    conn.close()

    if result:
        keywords, hashtags, tone, confidence, mentions, auto_gen, realtime, time_window = result

        # Properly handle empty strings and filter out empty values
        keyword_list = []
        if keywords and keywords.strip():
            keyword_list = [k.strip() for k in keywords.split(',') if k.strip()]

        hashtag_list = []
        if hashtags and hashtags.strip():
            hashtag_list = [h.strip() for h in hashtags.split(',') if h.strip()]

        return jsonify({
            'monitor_keywords': keyword_list,
            'monitor_hashtags': hashtag_list,
            'response_tone': tone or 'friendly',
            'confidence_threshold': confidence or 0.8,
            'monitor_mentions': bool(mentions),
            'auto_generate': bool(auto_gen),
            'realtime_monitoring': bool(realtime),
            'time_window_hours': time_window if time_window is not None else 2  # Default to 2 hours if None
        })
    else:
        return jsonify({'error': 'Account not found'}), 404

@app.route('/api/v1/social-accounts/<username>/settings', methods=['PUT'])
def update_account_settings(username):
    """Update account settings"""
    data = request.json

    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    # Clean and filter keywords and hashtags
    keywords = data.get('monitor_keywords', [])
    hashtags = data.get('monitor_hashtags', [])

    # Filter out empty strings and None values
    clean_keywords = [k.strip() for k in keywords if k and str(k).strip()]
    clean_hashtags = [h.strip() for h in hashtags if h and str(h).strip()]

    # Update settings
    cursor.execute('''
        UPDATE social_accounts
        SET monitor_keywords = ?, monitor_hashtags = ?, response_tone = ?,
            confidence_threshold = ?, monitor_mentions = ?, auto_generate = ?,
            realtime_monitoring = ?, time_window_hours = ?
        WHERE username = ?
    ''', (
        ','.join(clean_keywords),
        ','.join(clean_hashtags),
        data.get('response_tone', 'friendly'),
        data.get('confidence_threshold', 0.8),
        data.get('monitor_mentions', True),
        data.get('auto_generate', True),
        data.get('realtime_monitoring', True),
        data.get('time_window_hours') if 'time_window_hours' in data else 2,
        username
    ))

    if cursor.rowcount == 0:
        conn.close()
        return jsonify({'error': 'Account not found'}), 404

    conn.commit()
    conn.close()

    print(f"✅ Updated settings for @{username}")
    print(f"   Keywords: {data.get('monitor_keywords', [])}")
    print(f"   Hashtags: {data.get('monitor_hashtags', [])}")

    return jsonify({'success': True})

# Global monitoring state
monitoring_state = {
    'enabled': True,
    'last_manual_trigger': None,
    'total_manual_triggers': 0
}

@app.route('/api/v1/monitoring/status', methods=['GET'])
def get_monitoring_status():
    """Get current monitoring status"""
    return jsonify({
        'enabled': monitoring_state['enabled'],
        'last_manual_trigger': monitoring_state['last_manual_trigger'],
        'total_manual_triggers': monitoring_state['total_manual_triggers']
    })

@app.route('/api/v1/monitoring/toggle', methods=['POST'])
def toggle_monitoring():
    """Toggle monitoring on/off"""
    data = request.json
    enabled = data.get('enabled', not monitoring_state['enabled'])

    monitoring_state['enabled'] = enabled

    # Also update the database setting for the account
    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    cursor.execute('''
        UPDATE social_accounts
        SET realtime_monitoring = ?
        WHERE username = 'KarthikHar25823'
    ''', (enabled,))

    conn.commit()
    conn.close()

    status = "enabled" if enabled else "disabled"
    print(f"🔄 Monitoring {status} via admin UI")

    return jsonify({
        'success': True,
        'enabled': enabled,
        'message': f'Monitoring {status}'
    })

@app.route('/api/v1/monitoring/trigger', methods=['POST'])
def trigger_manual_monitoring():
    """Manually trigger a single monitoring cycle"""
    try:
        from start_real_time_monitoring import RealTimeMonitor
        from datetime import datetime

        print("🔄 Manual monitoring trigger requested...")

        # Create monitor instance
        monitor = RealTimeMonitor()

        # Run single cycle with manual trigger flag
        result = monitor.run_monitoring_cycle(manual_trigger=True)

        # Update state
        monitoring_state['last_manual_trigger'] = datetime.now().isoformat()
        monitoring_state['total_manual_triggers'] += 1

        print(f"✅ Manual monitoring cycle completed")
        print(f"   New mentions: {result.get('new_mentions', 0)}")

        return jsonify({
            'success': True,
            'new_mentions': result.get('new_mentions', 0),
            'message': 'Manual monitoring cycle completed',
            'timestamp': monitoring_state['last_manual_trigger']
        })

    except Exception as e:
        print(f"❌ Manual monitoring failed: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'Manual monitoring cycle failed'
        }), 500

@app.route('/api/v1/test/trigger-monitoring', methods=['POST'])
def trigger_monitoring():
    """Legacy endpoint - redirects to new manual trigger"""
    return trigger_manual_monitoring()

# Debug Logs Management
debug_logs = []
MAX_DEBUG_LOGS = 100

def add_debug_log(level, message):
    """Add a debug log entry"""
    global debug_logs
    from datetime import datetime

    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'level': level,
        'message': message
    }

    debug_logs.append(log_entry)

    # Keep only the last MAX_DEBUG_LOGS entries
    if len(debug_logs) > MAX_DEBUG_LOGS:
        debug_logs = debug_logs[-MAX_DEBUG_LOGS:]

    # Also print to console for immediate visibility
    print(f"[{log_entry['timestamp']}] {level.upper()}: {message}")

@app.route('/api/v1/debug/logs', methods=['GET'])
def get_debug_logs():
    """Get recent debug logs"""
    return jsonify({
        'logs': debug_logs[-50:],  # Return last 50 logs
        'total': len(debug_logs)
    })

@app.route('/api/v1/debug/logs', methods=['POST'])
def add_debug_log_endpoint():
    """Add a debug log entry"""
    data = request.get_json()
    if data and 'level' in data and 'message' in data:
        add_debug_log(data['level'], data['message'])
        return jsonify({'status': 'success'})
    return jsonify({'error': 'Invalid log data'}), 400

# Model Update Management Endpoints
@app.route('/api/v1/model/status', methods=['GET'])
def get_model_status():
    """Get current model status and update information"""
    try:
        from model_update_manager import ModelUpdateManager

        manager = ModelUpdateManager()
        status = manager.get_update_status()

        # Add current model info
        status.update({
            "model_name": "microsoft/Phi-3-mini-4k-instruct",
            "model_type": "Local SLM (Small Language Model)",
            "privacy_status": "🔒 100% Local Processing",
            "data_security": "✅ No external AI services",
            "license": "MIT License",
            "last_check": datetime.now().isoformat()
        })

        return jsonify(status)

    except Exception as e:
        return jsonify({
            "error": str(e),
            "model_name": "microsoft/Phi-3-mini-4k-instruct",
            "status": "unknown"
        }), 500

@app.route('/api/v1/model/check-updates', methods=['POST'])
def check_model_updates():
    """Check for available model updates"""
    try:
        from model_update_manager import ModelUpdateManager
        import asyncio

        manager = ModelUpdateManager()

        # Run async function in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(manager.check_for_updates())
        loop.close()

        return jsonify(result)

    except Exception as e:
        return jsonify({
            "error": str(e),
            "update_available": False
        }), 500

@app.route('/api/v1/model/update', methods=['POST'])
def update_model():
    """Update the model to latest version"""
    try:
        from model_update_manager import ModelUpdateManager
        import asyncio

        data = request.json or {}
        force = data.get('force', False)

        manager = ModelUpdateManager()

        # Run async function in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(manager.update_model(force=force))
        loop.close()

        if result.get('success'):
            print("✅ Model updated successfully - restart required")

        return jsonify(result)

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

# Advanced Response Filtering Endpoints
@app.route('/api/v1/responses/search', methods=['POST'])
def search_responses():
    """Advanced search and filtering for responses"""
    try:
        data = request.json or {}

        # Extract filter parameters
        search_term = data.get('search', '').lower()
        status_filter = data.get('status')
        sentiment_filter = data.get('sentiment')
        platform_filter = data.get('platform')
        priority_filter = data.get('priority')
        date_filter = data.get('date_range')
        confidence_filter = data.get('confidence')
        sort_by = data.get('sort_by', 'newest')
        page = data.get('page', 1)
        per_page = data.get('per_page', 10)

        # Use the global backend instance's db_path
        conn = sqlite3.connect(backend.db_path)
        cursor = conn.cursor()

        # Build dynamic query
        query = '''
            SELECT r.*, p.content, p.author_username, p.author_display_name,
                   p.sentiment, p.platform, p.posted_at, p.external_id, p.post_url, p.media_urls
            FROM responses r
            LEFT JOIN social_posts p ON r.social_post_id = p.id
            WHERE 1=1
        '''
        params = []

        # Apply enhanced search filters
        if search_term:
            # Support multiple search terms
            search_terms = [term.strip() for term in search_term.split() if term.strip()]

            for i, term in enumerate(search_terms):
                search_pattern = f'%{term}%'

                # Enhanced search across multiple fields
                search_conditions = [
                    'LOWER(r.response_text) LIKE ?',
                    'LOWER(p.content) LIKE ?',
                    'LOWER(p.author_username) LIKE ?',
                    'LOWER(p.author_display_name) LIKE ?',
                    'CAST(r.id AS TEXT) LIKE ?',
                    'CAST(r.social_post_id AS TEXT) LIKE ?',
                    'LOWER(p.external_id) LIKE ?',
                    'LOWER(r.status) LIKE ?',
                    'LOWER(p.sentiment) LIKE ?',
                    'LOWER(r.tone) LIKE ?',
                    'LOWER(p.platform) LIKE ?',
                    'DATE(r.created_at) LIKE ?',
                    'DATE(p.posted_at) LIKE ?',
                    'CAST(r.confidence_score AS TEXT) LIKE ?',
                    'LOWER(r.approved_by) LIKE ?'
                ]

                query += f' AND ({" OR ".join(search_conditions)})'
                # Add the search pattern for each condition
                params.extend([search_pattern] * len(search_conditions))

        if status_filter:
            query += ' AND r.status = ?'
            params.append(status_filter)

        if sentiment_filter:
            query += ' AND p.sentiment = ?'
            params.append(sentiment_filter)

        if platform_filter:
            query += ' AND p.platform = ?'
            params.append(platform_filter)

        if date_filter:
            if date_filter == 'today':
                query += ' AND DATE(r.created_at) = DATE("now")'
            elif date_filter == 'yesterday':
                query += ' AND DATE(r.created_at) = DATE("now", "-1 day")'
            elif date_filter == 'last7days':
                query += ' AND r.created_at >= DATE("now", "-7 days")'
            elif date_filter == 'last30days':
                query += ' AND r.created_at >= DATE("now", "-30 days")'

        if confidence_filter:
            if confidence_filter == 'high':
                query += ' AND CAST(r.confidence_score AS REAL) >= 0.9'
            elif confidence_filter == 'medium':
                query += ' AND CAST(r.confidence_score AS REAL) >= 0.7 AND CAST(r.confidence_score AS REAL) < 0.9'
            elif confidence_filter == 'low':
                query += ' AND CAST(r.confidence_score AS REAL) >= 0.5 AND CAST(r.confidence_score AS REAL) < 0.7'
            elif confidence_filter == 'very-low':
                query += ' AND CAST(r.confidence_score AS REAL) < 0.5'

        # Apply sorting
        if sort_by == 'newest':
            query += ' ORDER BY r.created_at DESC'
        elif sort_by == 'oldest':
            query += ' ORDER BY r.created_at ASC'
        elif sort_by == 'confidence':
            query += ' ORDER BY CAST(r.confidence_score AS REAL) DESC'
        elif sort_by == 'sentiment':
            query += ' ORDER BY CASE p.sentiment WHEN "negative" THEN 3 WHEN "neutral" THEN 2 WHEN "positive" THEN 1 END DESC'
        else:
            query += ' ORDER BY r.created_at DESC'

        # Get total count for pagination
        count_query = query.replace('SELECT r.*, p.content, p.author_username, p.author_display_name, p.sentiment, p.platform, p.posted_at, p.external_id, p.post_url, p.media_urls', 'SELECT COUNT(*)')
        cursor.execute(count_query, params)
        total_count = cursor.fetchone()[0]

        # Apply pagination
        offset = (page - 1) * per_page
        query += f' LIMIT {per_page} OFFSET {offset}'

        cursor.execute(query, params)
        rows = cursor.fetchall()

        # Format results (matching actual database schema)
        responses = []
        for row in rows:
            # Parse media URLs from JSON
            media_urls = []
            if row[19]:  # media_urls column
                try:
                    import json
                    media_urls = json.loads(row[19])
                except:
                    media_urls = []

            response = {
                'id': row[0],
                'social_post_id': row[1],
                'response_text': row[2],
                'tone': row[3],
                'confidence': row[4],
                'status': row[5],
                'approved_by': row[6],
                'approved_at': row[7],
                'posted_to_platform': row[8],
                'platform_response_id': row[9],
                'created_at': row[10],
                'original_post': {
                    'content': row[11],
                    'author_username': row[12],
                    'author_display_name': row[13],
                    'sentiment': row[14],
                    'platform': row[15],
                    'posted_at': row[16],
                    'external_id': row[17],
                    'post_url': row[18],
                    'media_urls': media_urls
                } if row[11] else None
            }
            responses.append(response)

        conn.close()

        return jsonify({
            'responses': responses,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'pages': (total_count + per_page - 1) // per_page
            },
            'filters_applied': {
                'search': search_term,
                'status': status_filter,
                'sentiment': sentiment_filter,
                'platform': platform_filter,
                'priority': priority_filter,
                'date_range': date_filter,
                'confidence': confidence_filter,
                'sort_by': sort_by
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/v1/twitter/config', methods=['GET', 'POST'])
def twitter_config():
    """Get or update Twitter configuration"""
    backend = RealTimeBackend()

    if request.method == 'GET':
        return jsonify({
            'character_limit': backend.twitter_config['character_limit'],
            'pro_character_limit': backend.twitter_config['pro_character_limit'],
            'is_pro_account': backend.twitter_config['is_pro_account'],
            'add_uniqueness': backend.twitter_config['add_uniqueness']
        })

    elif request.method == 'POST':
        data = request.json or {}

        # Update configuration
        if 'is_pro_account' in data:
            backend.twitter_config['is_pro_account'] = bool(data['is_pro_account'])
        if 'character_limit' in data:
            backend.twitter_config['character_limit'] = int(data['character_limit'])
        if 'add_uniqueness' in data:
            backend.twitter_config['add_uniqueness'] = bool(data['add_uniqueness'])

        return jsonify({
            'message': 'Twitter configuration updated',
            'config': backend.twitter_config
        })

@app.route('/api/v1/twitter/production-settings', methods=['POST'])
def set_production_settings():
    """Configure production-ready Twitter settings"""
    backend = RealTimeBackend()

    # Production-safe settings
    backend.twitter_config.update({
        'min_delay_between_posts': 1800,  # 30 minutes between posts
        'max_posts_per_hour': 3,  # Very conservative
        'development_mode': False,
        'human_like_delays': True,
        'add_uniqueness': True
    })

    return jsonify({
        'message': 'Production settings applied',
        'recommendations': {
            'posting_frequency': '3 posts per hour maximum',
            'delay_between_posts': '30 minutes minimum',
            'best_practices': [
                'Mix automated responses with manual tweets',
                'Vary response content to avoid repetition',
                'Monitor account health regularly',
                'Use Twitter Analytics to track engagement',
                'Respond to replies manually when possible'
            ]
        },
        'config': backend.twitter_config
    })

@app.route('/api/v1/twitter/account-health', methods=['GET'])
def get_account_health():
    """Get account health and posting recommendations"""
    backend = RealTimeBackend()

    conn = sqlite3.connect(backend.db_path)
    cursor = conn.cursor()

    # Get posting statistics
    cursor.execute('''
        SELECT
            COUNT(*) as total_posts,
            COUNT(CASE WHEN approved_at > datetime('now', '-24 hours') THEN 1 END) as posts_24h,
            COUNT(CASE WHEN approved_at > datetime('now', '-1 hour') THEN 1 END) as posts_1h,
            MAX(approved_at) as last_post
        FROM responses
        WHERE posted_to_platform = TRUE
    ''')

    stats = cursor.fetchone()
    total_posts, posts_24h, posts_1h, last_post = stats

    # Calculate health score
    health_score = 100
    warnings = []

    if posts_1h > backend.twitter_config['max_posts_per_hour']:
        health_score -= 30
        warnings.append(f"Exceeded hourly limit: {posts_1h}/{backend.twitter_config['max_posts_per_hour']}")

    if posts_24h > 24:  # More than 1 per hour average
        health_score -= 20
        warnings.append(f"High daily volume: {posts_24h} posts in 24h")

    # Check if we can post now
    can_post, rate_msg = backend._check_rate_limits()

    conn.close()

    return jsonify({
        'health_score': max(0, health_score),
        'can_post_now': can_post,
        'rate_limit_message': rate_msg,
        'statistics': {
            'total_posts': total_posts,
            'posts_last_24h': posts_24h,
            'posts_last_hour': posts_1h,
            'last_post_time': last_post
        },
        'warnings': warnings,
        'recommendations': {
            'current_status': 'Healthy' if health_score > 80 else 'Caution' if health_score > 50 else 'At Risk',
            'next_safe_post': 'Now' if can_post else rate_msg
        }
    })

@app.route('/api/v1/responses/export', methods=['POST'])
def export_responses():
    """Export filtered responses to CSV"""
    try:
        data = request.json or {}

        # Use same filtering logic as search
        search_result = search_responses()
        if search_result.status_code != 200:
            return search_result

        responses_data = search_result.get_json()
        responses = responses_data['responses']

        # Create CSV content
        import io
        import csv

        output = io.StringIO()
        writer = csv.writer(output)

        # Write headers
        headers = [
            'ID', 'Status', 'Created At', 'Original Post Content',
            'Original Post Author', 'Sentiment', 'Response Text',
            'Confidence', 'Platform'
        ]
        writer.writerow(headers)

        # Write data
        for response in responses:
            row = [
                response['id'],
                response['status'],
                response['created_at'],
                response['original_post']['content'] if response['original_post'] else '',
                response['original_post']['author_username'] if response['original_post'] else '',
                response['original_post']['sentiment'] if response['original_post'] else '',
                response['response_text'],
                response['confidence'],
                response['original_post']['platform'] if response['original_post'] else ''
            ]
            writer.writerow(row)

        csv_content = output.getvalue()
        output.close()

        # Return CSV as download
        from flask import make_response
        response = make_response(csv_content)
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = f'attachment; filename=responses_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

        return response

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/v1/responses/stats', methods=['GET'])
def get_response_stats():
    """Get response statistics for dashboard"""
    try:
        conn = sqlite3.connect(backend.db_path)
        cursor = conn.cursor()

        # Get overall stats
        cursor.execute('''
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
                COUNT(CASE WHEN status = 'posted' THEN 1 END) as posted,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
            FROM responses
        ''')

        stats = cursor.fetchone()

        # Get sentiment breakdown
        cursor.execute('''
            SELECT
                p.sentiment,
                COUNT(*) as count
            FROM responses r
            LEFT JOIN social_posts p ON r.social_post_id = p.id
            WHERE p.sentiment IS NOT NULL
            GROUP BY p.sentiment
        ''')

        sentiment_stats = {row[0]: row[1] for row in cursor.fetchall()}

        # Get platform breakdown
        cursor.execute('''
            SELECT
                p.platform,
                COUNT(*) as count
            FROM responses r
            LEFT JOIN social_posts p ON r.social_post_id = p.id
            WHERE p.platform IS NOT NULL
            GROUP BY p.platform
        ''')

        platform_stats = {row[0]: row[1] for row in cursor.fetchall()}

        # Get confidence distribution
        cursor.execute('''
            SELECT
                CASE
                    WHEN CAST(confidence_score AS REAL) >= 0.9 THEN 'high'
                    WHEN CAST(confidence_score AS REAL) >= 0.7 THEN 'medium'
                    WHEN CAST(confidence_score AS REAL) >= 0.5 THEN 'low'
                    ELSE 'very-low'
                END as confidence_level,
                COUNT(*) as count
            FROM responses
            WHERE confidence_score IS NOT NULL
            GROUP BY confidence_level
        ''')

        confidence_stats = {row[0]: row[1] for row in cursor.fetchall()}

        conn.close()

        return jsonify({
            'total_responses': stats[0],
            'status_breakdown': {
                'pending': stats[1],
                'approved': stats[2],
                'posted': stats[3],
                'rejected': stats[4]
            },
            'sentiment_breakdown': sentiment_stats,
            'platform_breakdown': platform_stats,
            'confidence_breakdown': confidence_stats
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# User Management Endpoints
if USER_MANAGEMENT_AVAILABLE:
    user_manager = UserManager()

    @app.route('/api/v1/auth/login', methods=['POST'])
    def login():
        """User login endpoint"""
        data = request.json
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'error': 'Username and password required'}), 400

        result = user_manager.authenticate_user(username, password)

        if result['success']:
            return jsonify({
                'success': True,
                'session_token': result['session_token'],
                'user': result['user']
            })
        else:
            return jsonify({'error': result['error']}), 401

    @app.route('/api/v1/auth/logout', methods=['POST'])
    @require_auth
    def logout():
        """User logout endpoint"""
        session_token = request.headers.get('Authorization', '').replace('Bearer ', '')
        user_manager.logout_user(session_token)
        return jsonify({'success': True})

    @app.route('/api/v1/auth/me', methods=['GET'])
    @require_auth
    def get_current_user():
        """Get current user info"""
        user = request.current_user

        # Get assigned social accounts for marketing users
        if user['role'] == 'marketing':
            social_accounts = user_manager.get_user_social_accounts(user['id'])
            user['assigned_accounts'] = social_accounts

        return jsonify({'user': user})

    @app.route('/api/v1/users/', methods=['GET'])
    @require_auth
    @require_role(['admin'])
    def get_users():
        """Get all users (admin only)"""
        users = user_manager.get_all_users()

        # Add assigned accounts for each marketing user
        for user in users:
            if user['role'] == 'marketing':
                user['assigned_accounts'] = user_manager.get_user_social_accounts(user['id'])

        return jsonify({'users': users})

    @app.route('/api/v1/users/', methods=['POST'])
    @require_auth
    @require_role(['admin'])
    def create_user():
        """Create new user (admin only)"""
        data = request.json

        required_fields = ['username', 'email', 'password', 'role', 'full_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        # Get social account IDs for marketing users
        social_account_ids = data.get('social_account_ids', [])

        result = user_manager.create_user(
            username=data['username'],
            email=data['email'],
            password=data['password'],
            role=data['role'],
            full_name=data['full_name'],
            created_by_id=request.current_user['id'],
            social_account_ids=social_account_ids if data['role'] == 'marketing' else None
        )

        if result['success']:
            return jsonify({'success': True, 'user_id': result['user_id']}), 201
        else:
            return jsonify({'error': result['error']}), 400

    @app.route('/api/v1/users/<int:user_id>', methods=['GET'])
    @require_auth
    @require_role(['admin'])
    def get_user(user_id):
        """Get user details (admin only)"""
        conn = sqlite3.connect(backend.db_path)
        cursor = conn.cursor()

        try:
            # Get user details
            cursor.execute('''
                SELECT id, username, email, full_name, role, is_active, created_at, last_login
                FROM users WHERE id = ?
            ''', (user_id,))

            user_row = cursor.fetchone()
            if not user_row:
                return jsonify({'error': 'User not found'}), 404

            user = {
                'id': user_row[0],
                'username': user_row[1],
                'email': user_row[2],
                'full_name': user_row[3],
                'role': user_row[4],
                'is_active': bool(user_row[5]),
                'created_at': user_row[6],
                'last_login': user_row[7]
            }

            # Get assigned social accounts for marketing users
            if user['role'] == 'marketing':
                user['assigned_accounts'] = user_manager.get_user_social_accounts(user_id)

            return jsonify({'user': user})

        except Exception as e:
            return jsonify({'error': str(e)}), 500
        finally:
            conn.close()

    @app.route('/api/v1/users/<int:user_id>/social-accounts', methods=['PUT'])
    @require_auth
    @require_role(['admin'])
    def assign_social_accounts(user_id):
        """Assign social accounts to marketing user (admin only)"""
        data = request.json
        social_account_ids = data.get('social_account_ids', [])

        conn = sqlite3.connect(backend.db_path)
        cursor = conn.cursor()

        try:
            # Verify user exists and is marketing role
            cursor.execute('SELECT role FROM users WHERE id = ?', (user_id,))
            user = cursor.fetchone()
            if not user:
                return jsonify({'error': 'User not found'}), 404

            if user[0] != 'marketing':
                return jsonify({'error': 'Can only assign accounts to marketing users'}), 400

            # Remove existing assignments
            cursor.execute('DELETE FROM user_social_accounts WHERE user_id = ?', (user_id,))

            # Add new assignments
            for account_id in social_account_ids:
                cursor.execute('''
                    INSERT INTO user_social_accounts (user_id, social_account_id, assigned_by)
                    VALUES (?, ?, ?)
                ''', (user_id, account_id, request.current_user['id']))

            conn.commit()
            return jsonify({'success': True})

        except Exception as e:
            conn.rollback()
            return jsonify({'error': str(e)}), 500
        finally:
            conn.close()

    @app.route('/api/v1/users/<int:user_id>', methods=['PUT'])
    @require_auth
    @require_role(['admin'])
    def update_user(user_id):
        """Update user details (admin only)"""
        data = request.json
        conn = sqlite3.connect(backend.db_path)
        cursor = conn.cursor()

        try:
            # Check if user exists
            cursor.execute('SELECT id, username FROM users WHERE id = ?', (user_id,))
            if not cursor.fetchone():
                return jsonify({'error': 'User not found'}), 404

            # Build update query dynamically based on provided fields
            update_fields = []
            update_values = []

            if 'username' in data:
                # Check if username is already taken by another user
                cursor.execute('SELECT id FROM users WHERE username = ? AND id != ?', (data['username'], user_id))
                if cursor.fetchone():
                    return jsonify({'error': 'Username already exists'}), 400
                update_fields.append('username = ?')
                update_values.append(data['username'])

            if 'email' in data:
                # Check if email is already taken by another user
                cursor.execute('SELECT id FROM users WHERE email = ? AND id != ?', (data['email'], user_id))
                if cursor.fetchone():
                    return jsonify({'error': 'Email already exists'}), 400
                update_fields.append('email = ?')
                update_values.append(data['email'])

            if 'full_name' in data:
                update_fields.append('full_name = ?')
                update_values.append(data['full_name'])

            if 'role' in data:
                if data['role'] not in ['admin', 'infra', 'marketing']:
                    return jsonify({'error': 'Invalid role'}), 400
                update_fields.append('role = ?')
                update_values.append(data['role'])

            if 'is_active' in data:
                update_fields.append('is_active = ?')
                update_values.append(1 if data['is_active'] else 0)

            if 'password' in data and data['password']:
                # Hash new password
                password_hash = user_manager.hash_password(data['password'])
                update_fields.append('password_hash = ?')
                update_values.append(password_hash)

            if not update_fields:
                return jsonify({'error': 'No fields to update'}), 400

            # Perform update
            update_values.append(user_id)
            update_query = f"UPDATE users SET {', '.join(update_fields)} WHERE id = ?"
            cursor.execute(update_query, update_values)

            conn.commit()
            return jsonify({'success': True})

        except Exception as e:
            conn.rollback()
            return jsonify({'error': str(e)}), 500
        finally:
            conn.close()

    @app.route('/api/v1/users/<int:user_id>', methods=['DELETE'])
    @require_auth
    @require_role(['admin'])
    def delete_user(user_id):
        """Delete user (admin only)"""
        conn = sqlite3.connect(backend.db_path)
        cursor = conn.cursor()

        try:
            # Check if user exists
            cursor.execute('SELECT id FROM users WHERE id = ?', (user_id,))
            if not cursor.fetchone():
                return jsonify({'error': 'User not found'}), 404

            # Don't allow deleting the last admin
            cursor.execute('SELECT COUNT(*) FROM users WHERE role = "admin" AND is_active = 1')
            admin_count = cursor.fetchone()[0]

            cursor.execute('SELECT role FROM users WHERE id = ?', (user_id,))
            user_role = cursor.fetchone()[0]

            if user_role == 'admin' and admin_count <= 1:
                return jsonify({'error': 'Cannot delete the last admin user'}), 400

            # Soft delete user
            cursor.execute('UPDATE users SET is_active = 0 WHERE id = ?', (user_id,))

            # Remove social account assignments
            cursor.execute('DELETE FROM user_social_accounts WHERE user_id = ?', (user_id,))

            # Remove active sessions
            cursor.execute('DELETE FROM user_sessions WHERE user_id = ?', (user_id,))

            conn.commit()
            return jsonify({'success': True})

        except Exception as e:
            conn.rollback()
            return jsonify({'error': str(e)}), 500
        finally:
            conn.close()

# Secure Credential Management Endpoints
if SECURE_CREDENTIALS_AVAILABLE and USER_MANAGEMENT_AVAILABLE:
    @app.route('/api/v1/credentials/', methods=['GET'])
    @require_auth
    @require_role('admin')
    def get_credentials():
        """Get all stored credentials (admin only)"""
        try:
            conn = sqlite3.connect(backend.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, name, platform, description, is_active, is_validated,
                       last_validated_at, created_at, created_by
                FROM secure_credentials
                ORDER BY created_at DESC
            ''')

            credentials = []
            for row in cursor.fetchall():
                credentials.append({
                    'id': row[0],
                    'name': row[1],
                    'platform': row[2],
                    'description': row[3],
                    'is_active': bool(row[4]),
                    'is_validated': bool(row[5]),
                    'last_validated_at': row[6],
                    'created_at': row[7],
                    'created_by': row[8]
                })

            conn.close()
            return jsonify({'credentials': credentials})

        except Exception as e:
            return jsonify({'error': f'Failed to retrieve credentials: {str(e)}'}), 500

    @app.route('/api/v1/credentials/', methods=['POST'])
    @require_auth
    @require_role('admin')
    def create_credential():
        """Create new encrypted credential (admin only)"""
        try:
            data = request.json

            # Validate required fields
            required_fields = ['name', 'platform', 'credentials']
            for field in required_fields:
                if field not in data:
                    return jsonify({'error': f'Missing required field: {field}'}), 400

            # Encrypt credentials
            credential_manager = get_credential_manager()

            # Validate credentials for platform
            if not credential_manager.validate_credentials(data['platform'], data['credentials']):
                return jsonify({'error': f'Invalid credentials for platform {data["platform"]}'}), 400

            encrypted_creds = credential_manager.encrypt_credentials(data['credentials'])

            # Store in database
            conn = sqlite3.connect(backend.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO secure_credentials
                (name, platform, description, encrypted_credentials, is_validated, created_by)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                data['name'],
                data['platform'],
                data.get('description', ''),
                encrypted_creds,
                1,  # Mark as validated since we just validated
                'admin'  # For now, use admin as default
            ))

            credential_id = cursor.lastrowid
            conn.commit()
            conn.close()

            # Restart Twitter client if this is a Twitter credential
            if data['platform'] == 'twitter':
                backend.setup_twitter_client()

            return jsonify({
                'success': True,
                'credential_id': credential_id,
                'message': 'Credential created successfully'
            })

        except Exception as e:
            return jsonify({'error': f'Failed to create credential: {str(e)}'}), 500

    @app.route('/api/v1/credentials/<int:credential_id>', methods=['GET'])
    @require_auth
    @require_role('admin')
    def get_credential(credential_id):
        """Get specific credential details (admin only)"""
        try:
            conn = sqlite3.connect(backend.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, name, platform, description, encrypted_credentials, is_active,
                       is_validated, last_validated_at, created_at, created_by
                FROM secure_credentials WHERE id = ?
            ''', (credential_id,))

            result = cursor.fetchone()
            conn.close()

            if not result:
                return jsonify({'error': 'Credential not found'}), 404

            credential = {
                'id': result[0],
                'name': result[1],
                'platform': result[2],
                'description': result[3],
                'is_active': bool(result[5]),
                'is_validated': bool(result[6]),
                'last_validated_at': result[7],
                'created_at': result[8],
                'created_by': result[9]
            }

            # Get masked credentials for display
            if SECURE_CREDENTIALS_AVAILABLE:
                try:
                    credential_manager = get_credential_manager()
                    decrypted_creds = credential_manager.decrypt_credentials(result[4])
                    credential['masked_credentials'] = credential_manager.mask_credentials(decrypted_creds)
                except Exception as e:
                    credential['masked_credentials'] = {'error': 'Failed to decrypt'}

            return jsonify({'credential': credential})

        except Exception as e:
            return jsonify({'error': f'Failed to retrieve credential: {str(e)}'}), 500

    @app.route('/api/v1/credentials/<int:credential_id>', methods=['PUT'])
    @require_auth
    @require_role('admin')
    def update_credential(credential_id):
        """Update credential (admin only)"""
        try:
            data = request.json

            conn = sqlite3.connect(backend.db_path)
            cursor = conn.cursor()

            # Check if credential exists
            cursor.execute('SELECT encrypted_credentials, platform FROM secure_credentials WHERE id = ?', (credential_id,))
            result = cursor.fetchone()

            if not result:
                conn.close()
                return jsonify({'error': 'Credential not found'}), 404

            current_encrypted_creds, platform = result

            # Update basic fields
            update_fields = []
            update_values = []

            if 'name' in data:
                update_fields.append('name = ?')
                update_values.append(data['name'])

            if 'description' in data:
                update_fields.append('description = ?')
                update_values.append(data['description'])

            if 'is_active' in data:
                update_fields.append('is_active = ?')
                update_values.append(data['is_active'])

            # Handle credential updates
            if 'credentials' in data and SECURE_CREDENTIALS_AVAILABLE:
                credential_manager = get_credential_manager()

                # Get current credentials
                current_creds = credential_manager.decrypt_credentials(current_encrypted_creds)

                # Update only provided fields
                for key, value in data['credentials'].items():
                    if value:  # Only update non-empty values
                        current_creds[key] = value

                # Validate updated credentials
                if not credential_manager.validate_credentials(platform, current_creds):
                    conn.close()
                    return jsonify({'error': f'Invalid credentials for platform {platform}'}), 400

                # Encrypt updated credentials
                new_encrypted_creds = credential_manager.encrypt_credentials(current_creds)
                update_fields.append('encrypted_credentials = ?')
                update_values.append(new_encrypted_creds)

                # Reset validation status
                update_fields.append('is_validated = ?')
                update_values.append(False)

            if update_fields:
                update_fields.append('updated_at = datetime("now")')
                update_values.append(credential_id)

                query = f'UPDATE secure_credentials SET {", ".join(update_fields)} WHERE id = ?'
                cursor.execute(query, update_values)
                conn.commit()

            conn.close()

            # Restart Twitter client if this was a Twitter credential
            if platform == 'twitter' and 'credentials' in data:
                backend.setup_twitter_client()

            return jsonify({
                'success': True,
                'message': 'Credential updated successfully'
            })

        except Exception as e:
            return jsonify({'error': f'Failed to update credential: {str(e)}'}), 500

    @app.route('/api/v1/credentials/<int:credential_id>/validate', methods=['POST'])
    @require_auth
    @require_role('admin')
    def validate_credential(credential_id):
        """Validate credential format and completeness (admin only)"""
        try:
            conn = sqlite3.connect(backend.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT encrypted_credentials, platform FROM secure_credentials WHERE id = ?', (credential_id,))
            result = cursor.fetchone()

            if not result:
                conn.close()
                return jsonify({'error': 'Credential not found'}), 404

            encrypted_creds, platform = result

            if SECURE_CREDENTIALS_AVAILABLE:
                credential_manager = get_credential_manager()

                try:
                    # Decrypt and validate
                    decrypted_creds = credential_manager.decrypt_credentials(encrypted_creds)
                    is_valid = credential_manager.validate_credentials(platform, decrypted_creds)

                    # Update validation status
                    cursor.execute('''
                        UPDATE secure_credentials
                        SET is_validated = ?, last_validated_at = datetime("now"), validation_error = ?
                        WHERE id = ?
                    ''', (is_valid, None if is_valid else f'Invalid credentials for {platform}', credential_id))

                    conn.commit()
                    conn.close()

                    return jsonify({
                        'is_valid': is_valid,
                        'validation_error': None if is_valid else f'Invalid credentials for {platform}',
                        'validated_at': datetime.now().isoformat()
                    })

                except Exception as e:
                    conn.close()
                    return jsonify({
                        'is_valid': False,
                        'validation_error': str(e),
                        'validated_at': datetime.now().isoformat()
                    })
            else:
                conn.close()
                return jsonify({'error': 'Credential validation not available'}), 503

        except Exception as e:
            return jsonify({'error': f'Failed to validate credential: {str(e)}'}), 500

if __name__ == '__main__':
    print("🚀 Starting Agentic ORM Backend...")
    print("✅ Database initialized")
    print("✅ Twitter client ready")
    if USER_MANAGEMENT_AVAILABLE:
        print("✅ User management enabled")
        print("👤 Default admin: username=admin, password=admin123")
    print("📱 Admin UI will show real data")
    print("🌐 Backend running on http://localhost:8001")
    print("📚 API Endpoints:")
    print("   - GET  /health")
    print("   - GET  /api/v1/social-accounts/")
    print("   - GET  /api/v1/responses/")
    print("   - POST /api/v1/responses/{id}/approve")
    print("   - POST /api/v1/responses/{id}/regenerate")
    if USER_MANAGEMENT_AVAILABLE:
        print("   - POST /api/v1/auth/login")
        print("   - POST /api/v1/auth/logout")
        print("   - GET  /api/v1/auth/me")
        print("   - GET  /api/v1/users/")
        print("   - POST /api/v1/users/")
        print("   - PUT  /api/v1/users/{id}/social-accounts")
        print("   - DELETE /api/v1/users/{id}")
    if SECURE_CREDENTIALS_AVAILABLE:
        print("   - GET  /api/v1/credentials/")
        print("   - POST /api/v1/credentials/")
        print("   - DELETE /api/v1/credentials/{id}")
    print("")

    app.run(host='0.0.0.0', port=8001, debug=True)
