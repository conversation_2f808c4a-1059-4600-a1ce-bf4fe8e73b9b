/Users/<USER>/Documents/augment-projects/Agentic Social Handler/airline_phi3_env/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
2025-06-24 14:54:28,899 - phi3_airline_service - INFO - 🎯 Initializing Phi-3 Mini for airline customer service
2025-06-24 14:54:28,942 - phi3_airline_service - INFO - 📱 Device: cpu
2025-06-24 14:54:28,942 - phi3_airline_service - INFO - 💾 Cache directory: ./models
✅ Phi-3 Mini service available
✅ User management available
✅ Secure credential management available
✅ Twitter client ready (using secure credentials)
✅ Phi-3 Mini service ready (will initialize on first use)
🚀 Starting Agentic ORM Backend...
✅ Database initialized
✅ Twitter client ready
✅ User management enabled
👤 Default admin: username=admin, password=admin123
📱 Admin UI will show real data
🌐 Backend running on http://localhost:8001
📚 API Endpoints:
   - GET  /health
   - GET  /api/v1/social-accounts/
   - GET  /api/v1/responses/
   - POST /api/v1/responses/{id}/approve
   - POST /api/v1/responses/{id}/regenerate
   - POST /api/v1/auth/login
   - POST /api/v1/auth/logout
   - GET  /api/v1/auth/me
   - GET  /api/v1/users/
   - POST /api/v1/users/
   - PUT  /api/v1/users/{id}/social-accounts
   - DELETE /api/v1/users/{id}
   - GET  /api/v1/credentials/
   - POST /api/v1/credentials/
   - DELETE /api/v1/credentials/{id}

 * Serving Flask app 'real_time_backend'
 * Debug mode: on
2025-06-24 14:54:28,993 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://*************:8001
2025-06-24 14:54:28,993 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-24 14:54:28,994 - werkzeug - INFO -  * Restarting with stat
/Users/<USER>/Documents/augment-projects/Agentic Social Handler/airline_phi3_env/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
2025-06-24 14:54:33,994 - phi3_airline_service - INFO - 🎯 Initializing Phi-3 Mini for airline customer service
2025-06-24 14:54:33,995 - phi3_airline_service - INFO - 📱 Device: cpu
2025-06-24 14:54:33,995 - phi3_airline_service - INFO - 💾 Cache directory: ./models
2025-06-24 14:54:34,030 - werkzeug - WARNING -  * Debugger is active!
2025-06-24 14:54:34,040 - werkzeug - INFO -  * Debugger PIN: 569-************-06-24 14:54:34,077 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:54:34] "GET /health HTTP/1.1" 200 -
2025-06-24 14:54:35,238 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:54:35] "GET /health HTTP/1.1" 200 -
2025-06-24 14:54:35,257 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:54:35] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-24 14:54:35,287 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:54:35] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-24 14:54:57,442 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:54:57] "OPTIONS /api/v1/auth/me HTTP/1.1" 200 -
2025-06-24 14:54:57,452 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:54:57] "[31m[1mGET /api/v1/auth/me HTTP/1.1[0m" 401 -
2025-06-24 14:55:02,604 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:55:02] "OPTIONS /api/v1/auth/login HTTP/1.1" 200 -
2025-06-24 14:55:03,045 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:55:03] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-24 14:55:04,818 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:55:04] "OPTIONS /api/v1/auth/me HTTP/1.1" 200 -
2025-06-24 14:55:04,826 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:55:04] "GET /api/v1/auth/me HTTP/1.1" 200 -
2025-06-24 14:55:04,889 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:55:04] "GET /api/v1/monitoring/status HTTP/1.1" 200 -
2025-06-24 14:55:04,891 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:55:04] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-24 14:55:04,894 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:55:04] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-24 14:55:04,898 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:55:04] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-24 14:55:06,891 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:55:06] "GET /health HTTP/1.1" 200 -
2025-06-24 14:56:43,936 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:56:43] "OPTIONS /api/v1/auth/me HTTP/1.1" 200 -
2025-06-24 14:56:44,418 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:56:44] "GET /api/v1/auth/me HTTP/1.1" 200 -
2025-06-24 14:56:44,770 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:56:44] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-24 14:56:44,815 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:56:44] "GET /api/v1/monitoring/status HTTP/1.1" 200 -
2025-06-24 14:56:44,883 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:56:44] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-24 14:56:44,928 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:56:44] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-24 14:56:46,972 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:56:46] "GET /health HTTP/1.1" 200 -
2025-06-24 14:56:48,017 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:56:48] "OPTIONS /api/v1/users/ HTTP/1.1" 200 -
2025-06-24 14:56:48,062 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:56:48] "GET /api/v1/users/ HTTP/1.1" 200 -
2025-06-24 14:56:56,368 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:56:56] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-24 14:56:56,385 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:56:56] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-24 14:57:14,439 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:57:14] "OPTIONS /api/v1/responses/41/regenerate HTTP/1.1" 200 -
2025-06-24 14:57:14,481 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:57:14] "POST /api/v1/responses/41/regenerate HTTP/1.1" 200 -
2025-06-24 14:57:14,495 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:57:14] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-24 14:58:06,769 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:58:06] "OPTIONS /api/v1/responses/42/regenerate HTTP/1.1" 200 -
2025-06-24 14:58:06,796 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:58:06] "POST /api/v1/responses/42/regenerate HTTP/1.1" 200 -
2025-06-24 14:58:06,809 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:58:06] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-24 14:58:06,820 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:58:06] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-24 14:58:44,585 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 14:58:44] "GET /health HTTP/1.1" 200 -
2025-06-24 15:00:44,587 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 15:00:44] "GET /health HTTP/1.1" 200 -
2025-06-24 15:02:45,242 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 15:02:45] "GET /health HTTP/1.1" 200 -
2025-06-24 15:04:45,247 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 15:04:45] "GET /health HTTP/1.1" 200 -
/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py:216: UserWarning: resource_tracker: There appear to be 1 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py:216: UserWarning: resource_tracker: There appear to be 1 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
