#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create multilingual sample tweets for demo purposes
"""

import sqlite3
import json
from datetime import datetime, timedelta
import random

# Sample tweets in multiple languages
SAMPLE_TWEETS = {
    "hindi": [
        # Negative - Flight Delays
        {
            "content": "@AirlineSupport मेरी फ्लाइट AI456 3 घंटे से देर हो रही है! मैं गोल्ड मेंबर हूं और मुझे मुआवजा चाहिए। यह बहुत खराब सेवा है! #FlightDelay",
            "author": "राज_शर्मा",
            "sentiment": "negative",
            "scenario": "flight_delay_compensation"
        },
        {
            "content": "@AirlineSupport दिल्ली से मुंबई की फ्लाइट रद्द हो गई है। कोई जानकारी नहीं दी गई। यह क्या तरीका है? #Cancelled #BadService",
            "author": "प्रिया_गुप्ता",
            "sentiment": "negative", 
            "scenario": "flight_cancellation"
        },
        # Positive
        {
            "content": "@AirlineSupport बहुत बढ़िया सेवा! क्रू बहुत मददगार था और खाना भी अच्छा था। धन्यवाद! ✈️ #GreatService",
            "author": "अमित_कुमार",
            "sentiment": "positive",
            "scenario": "positive_feedback"
        },
        # Questions
        {
            "content": "@AirlineSupport क्या मैं अपनी फ्लाइट का समय बदल सकता हूं? कल की बुकिंग है। कृपया बताएं। #Help",
            "author": "सुनीता_देवी",
            "sentiment": "neutral_question",
            "scenario": "booking_inquiry"
        }
    ],
    
    "spanish": [
        # Negative - Flight Delays  
        {
            "content": "@AirlineSupport Mi vuelo IB789 lleva 5 horas de retraso! Soy miembro Platino y exijo compensación. Esto es inaceptable! #Retraso #Compensacion",
            "author": "carlos_madrid",
            "sentiment": "negative",
            "scenario": "flight_delay_compensation"
        },
        {
            "content": "@AirlineSupport Perdieron mi equipaje en Barcelona. Llevo 2 días sin noticias. Necesito ayuda urgente! #EquipajePerdido",
            "author": "maria_barcelona", 
            "sentiment": "negative",
            "scenario": "baggage_lost"
        },
        # Positive
        {
            "content": "@AirlineSupport Excelente vuelo de Madrid a París! La tripulación fue fantástica y el servicio impecable. Gracias! 🙏 #ExcelenteServicio",
            "author": "ana_sevilla",
            "sentiment": "positive", 
            "scenario": "positive_feedback"
        },
        # Questions
        {
            "content": "@AirlineSupport ¿Puedo cambiar mi asiento sin costo adicional? Soy miembro Elite. Gracias por la información. #Consulta",
            "author": "diego_valencia",
            "sentiment": "neutral_question",
            "scenario": "seat_inquiry"
        }
    ],
    
    "french": [
        # Negative - Flight Delays
        {
            "content": "@AirlineSupport Mon vol AF234 a 4 heures de retard! Je suis membre Platine et j'exige une compensation. Service inacceptable! #Retard #Compensation",
            "author": "pierre_paris",
            "sentiment": "negative",
            "scenario": "flight_delay_compensation"
        },
        {
            "content": "@AirlineSupport Vol annulé à la dernière minute sans explication. Mes vacances sont gâchées! Que comptez-vous faire? #VolAnnule #Colere",
            "author": "sophie_lyon",
            "sentiment": "negative",
            "scenario": "flight_cancellation"
        },
        # Positive
        {
            "content": "@AirlineSupport Merci pour ce vol fantastique Paris-Nice! L'équipage était formidable et le repas délicieux. Bravo! 👏 #ExcellentService",
            "author": "julien_nice",
            "sentiment": "positive",
            "scenario": "positive_feedback"
        },
        # Questions
        {
            "content": "@AirlineSupport Comment puis-je surclasser mon billet? J'ai une réunion importante. Merci de votre aide. #Aide #Surclassement",
            "author": "claire_toulouse",
            "sentiment": "neutral_question", 
            "scenario": "upgrade_inquiry"
        }
    ]
}

def create_sample_tweets():
    """Create sample tweets in the database"""
    conn = sqlite3.connect('social_handler.db')
    cursor = conn.cursor()
    
    try:
        # Clear existing sample tweets (optional)
        print("🗑️ Clearing existing sample tweets...")
        cursor.execute("DELETE FROM social_posts WHERE author_username LIKE '%sample%' OR author_username IN ('राज_शर्मा', 'प्रिया_गुप्ता', 'अमित_कुमार', 'सुनीता_देवी', 'carlos_madrid', 'maria_barcelona', 'ana_sevilla', 'diego_valencia', 'pierre_paris', 'sophie_lyon', 'julien_nice', 'claire_toulouse')")
        
        tweet_id = 1000  # Start with high ID to avoid conflicts
        
        for language, tweets in SAMPLE_TWEETS.items():
            print(f"🌍 Creating {language.title()} tweets...")
            
            for tweet in tweets:
                # Create social post
                created_at = datetime.now() - timedelta(hours=random.randint(1, 72))
                
                cursor.execute('''
                    INSERT INTO social_posts
                    (external_id, content, author_username, author_display_name, posted_at, platform, media_urls, language, scenario)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    f"demo_{tweet_id}",
                    tweet['content'],
                    tweet['author'],
                    tweet['author'].replace('_', ' ').title(),
                    created_at.isoformat(),
                    'twitter',
                    json.dumps([]) if tweet['scenario'] != 'baggage_lost' else json.dumps(['https://example.com/baggage_damage.jpg']),
                    language,
                    tweet['scenario']
                ))
                
                post_id = cursor.lastrowid
                
                # Create corresponding response (pending)
                cursor.execute('''
                    INSERT INTO responses 
                    (social_post_id, response_text, confidence_score, tone, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    post_id,
                    f"Auto-generated response for {language} tweet (pending review)",
                    random.uniform(0.7, 0.9),
                    "professional",
                    "pending",
                    created_at.isoformat()
                ))
                
                tweet_id += 1
                
        conn.commit()
        print(f"✅ Created {tweet_id - 1000} sample tweets across {len(SAMPLE_TWEETS)} languages")
        
        # Show summary
        cursor.execute("SELECT language, COUNT(*) FROM social_posts WHERE language IN ('hindi', 'spanish', 'french') GROUP BY language")
        results = cursor.fetchall()
        
        print("\n📊 Sample Tweet Summary:")
        for lang, count in results:
            print(f"   {lang.title()}: {count} tweets")
            
    except Exception as e:
        print(f"❌ Error creating sample tweets: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    create_sample_tweets()
